import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from "react";
import { Box, Popover, Typography, IconButton, Tooltip } from "@mui/material";
import JoditEditor from "jodit-react";

import RTE from "./RTE";
import useDrawerStore, { IRTEContainer, TSectionType } from "../../../../store/drawerStore";
import { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { copyicon, deleteicon, editicon } from "../../../../assets/icons/icons";
import { useTranslation } from 'react-i18next';

interface RTEsectionProps {
	items: IRTEContainer;
	boxRef: React.RefObject<HTMLDivElement>;
	handleFocus: (id: string) => void;
	handleeBlur: (id: string) => void;

	isPopoverOpen: boolean;
	setIsPopoverOpen: (params: boolean) => void;
	currentRTEFocusedId: string;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
	(
		{
			items: { id, rteBoxValue },
			boxRef,
			handleFocus,
			handleeBlur,

			isPopoverOpen,
			setIsPopoverOpen,
			currentRTEFocusedId,
		},
		ref
	) => {
		const { t: translate } = useTranslation();
		const {
			setIsUnSavedChanges,
			setHtmlContent,
			textvaluess,
			setTextvaluess,
			backgroundC,
			setBackgroundC,
			Bbordercolor,
			BborderSize,
			bpadding,
			sectionColor,
			setSectionColor,
			handleTooltipRTEBlur,
			handleTooltipRTEValue,
			handleRTEDeleteSection,
			handleRTECloneSection,
			tooltip,
			currentStep,
			toolTipGuideMetaData,
		} = useDrawerStore((state) => state);
		// Removed unused state variables since we're using Jodit editor directly

		const [isEditing, setIsEditing] = useState(false);
		const [toolbarVisible, setToolbarVisible] = useState(false);
		const editorRef = useRef(null);
		const containerRef = useRef<HTMLDivElement | null>(null);

		// State to track content for dynamic icon positioning
		const [contentState, setContentState] = useState<{ isEmpty: boolean, isScrollable: boolean }>({ isEmpty: true, isScrollable: false });

		// Memoize Jodit config to prevent re-renders and focus loss
		const joditConfig = useMemo((): any => ({
			readonly: false,
			// Hide main toolbar by default, controlled by toolbarVisible state
			toolbar: toolbarVisible,
			// Enable inline toolbar for text selection
			// toolbarInline: true,
			// toolbarInlineForSelection: true,
			// toolbarInlineDisabledButtons: ['source', 'fullsize'],
			// toolbarInlineDisableFor: [],
			toolbarSticky: false,
			toolbarAdaptive: false,
			// Inline toolbar width configuration
			toolbarButtonSize: 'small',
			toolbarInlineWidth: 500,
			toolbarInlineMaxWidth: 600,
			toolbarInlineMinWidth: 450,
			// Additional popup configuration for inline toolbar
			popup: {
				selection: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link'],
				toolbar: {
					width: 500,
					maxWidth: 600,
					minWidth: 450
				}
			},
			showCharsCounter: false,
			showWordsCounter: false,
			showXPathInStatusbar: false,
			statusbar: false,
			pastePlain: true,
			askBeforePasteHTML: false,
			askBeforePasteFromWord: false,
			buttons: [
				'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
				'font', 'fontsize', 'link',
				{
					name: 'more',
					iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
					list: [
						'image', 'video', 'table',
						'align', 'undo', 'redo', '|',
						'hr', 'eraser', 'copyformat',
						'symbol', 'print', 'superscript', 'subscript', '|',
						'outdent', 'indent', 'paragraph',
					]
				}
			],
			autofocus: false,
			// Enable auto-resize behavior
			height: 'auto',
			minHeight: toolbarVisible ? 130 : 28, // 130px when toolbar visible, 28px when hidden
			maxHeight: 150,
			// Fix dialog positioning by setting popup root to document body
			popupRoot: document.body,
			// Ensure dialogs appear in correct position
			zIndex: 100000,
			globalFullSize: false,
			// Add custom CSS to ensure text is visible
			style: {
				color: '#000000 !important',
				backgroundColor: '#ffffff',
				fontFamily: 'Poppins, sans-serif',
			},
			// Override editor styles to ensure text visibility
			editorCssClass: 'jodit-tooltip-editor',
			// Set default content styling
			enter: 'p' as const,
			// Fix link dialog positioning
			link: {
				followOnDblClick: false,
				processVideoLink: true,
				processPastedLink: true,
				openInNewTabCheckbox: true,
				noFollowCheckbox: false,
				modeClassName: 'input' as const,
			},
			// Dialog configuration
			dialog: {
				zIndex: 100001,
			},
			controls: {
				font: {
					list: {
						"Poppins, sans-serif": "Poppins",
						"Roboto, sans-serif": "Roboto",
						"Comic Sans MS, sans-serif": "Comic Sans MS",
						"Open Sans, sans-serif": "Open Sans",
						"Calibri, sans-serif": "Calibri",
						"Century Gothic, sans-serif": "Century Gothic",
					}
				}
			}
		}), [id, toolbarVisible]);

		// Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)
		const isContentEmpty = (content: string): boolean => {
			if (!content) return true;
			// Remove HTML tags and check if there's actual text content
			const textContent = content.replace(/<[^>]*>/g, '').trim();
			return textContent.length === 0;
		};

		// Helper function to check if content is scrollable
		const isContentScrollable = (): boolean => {
			if (containerRef?.current) {
				const workplace = containerRef.current.querySelector('.jodit-workplace');
				if (workplace) {
					return workplace.scrollHeight > workplace.clientHeight;
				}
			}
			return false;
		};

		// Update content state for dynamic icon positioning
		const updateContentState = (content: string) => {
			const isEmpty = isContentEmpty(content);
			const isScrollable = isContentScrollable();

			setContentState({ isEmpty, isScrollable });
		};

		// Toggle toolbar function
		const toggleToolbar = () => {
			setToolbarVisible(!toolbarVisible);
		};

		// Dynamic CSS based on toolbar visibility
		const dynamicCSS = `
			/* Hide the add new line button */
			.jodit-add-new-line {
				display: none !important;
			}
			/* Tooltip/Hotspot specific Jodit editor styles */
			.jodit-tooltip-editor .jodit-wysiwyg {
				color: #000000 !important;
				background-color: #ffffff !important;
				line-height: 1.4 !important;
				padding: 8px !important;
			}
			/* Height and scrolling behavior for tooltip RTE */
			.jodit-workplace {
				min-height: ${toolbarVisible ? '130px' : '28px'} !important;
				max-height: 150px !important;
				overflow-y: auto !important;
				line-height: 1.4 !important;
			}
			.jodit-tooltip-editor .jodit-container {
				border: none !important;
			}
			/* Target the specific jodit container class combination */
			.jodit-container.jodit.jodit_theme_default.jodit-wysiwyg_mode {
				border: 0 !important;
			}
			.jodit-tooltip-editor .jodit-wysiwyg p {
				color: #000000 !important;
				margin: 0 0 4px 0 !important;
				padding: 0 !important;
				line-height: 1.4 !important;
			}
			/* Enhanced scrollbar styling for tooltip RTE */
			.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar {
				width: 6px !important;
			}
			.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-track {
				background: #f1f1f1 !important;
				border-radius: 3px !important;
			}
			.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb {
				background: #c1c1c1 !important;
				border-radius: 3px !important;
			}
			.jodit-tooltip-editor .jodit-workplace::-webkit-scrollbar-thumb:hover {
				background: #a8a8a8 !important;
			}
			/* Ensure text is visible in all states */
			.jodit-wysiwyg[contenteditable="true"] {
				color: #000000 !important;
				background-color: #ffffff !important;
				line-height: 1.4 !important;
				padding: 8px !important;
			}
			.jodit-wysiwyg[contenteditable="true"] * {
				color: #000000 !important;
			}
		`;

		// Memoize onChange handler to prevent re-renders
		const handleContentChange = useCallback((newContent: string) => {
			handleTooltipRTEValue(id, newContent);
			// Update content state for dynamic icon positioning
			updateContentState(newContent);
		}, [id, handleTooltipRTEValue, updateContentState]);

		// Initialize content state on mount
		useEffect(() => {
			updateContentState(rteBoxValue || "");
		}, [rteBoxValue]);

		// const handleInput = () => {
		// 	// Update the content state when user types
		// 	if (boxRef.current) {
		// 		const updatedContent = boxRef.current.innerHTML;
		// 		setContent(updatedContent); // Store the content in state
		// 		setHtmlContent(updatedContent); // Update the HTML content
		// 		setIsUnSavedChanges(true);
		// 		preserveCaretPosition();
		// 	}
		// };
		// Removed caret position functions since we're using Jodit editor

		// useEffect(() => {
		// 	// After content update, restore the cursor position
		// 	restoreCaretPosition();
		// }, [boxRef.current?.innerHTML]); // Run when content changes

		// Remove section

		// useEffect(() => {
		// 	if (boxRef.current?.innerHTML?.trim()) {
		// 		setIsUnSavedChanges(true);
		// 	}
		// }, [boxRef.current?.innerHTML?.trim()]);

		// Removed useEffect since we're using Jodit editor directly

		// Auto-focus the editor when editing mode is activated
		useEffect(() => {
			if (isEditing && editorRef.current) {
				setTimeout(() => {
					(editorRef.current as any).editor.focus();
				}, 50);
			}
		}, [isEditing]);

		// Handle clicks outside the editor to close editing mode
		useEffect(() => {
			const handleClickOutside = (event: MouseEvent) => {
				const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
				const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
				const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
				const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
				const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
				const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
				const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
				const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
				const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

				// Check if the target is inside the editor or related elements
				if (
					containerRef.current &&
					!containerRef.current.contains(event.target as Node) && // Click outside the editor container
					!isInsidePopup && // Click outside the popup
					!isInsideJoditPopup && // Click outside the WYSIWYG editor
					!isInsideWorkplacePopup && // Click outside the workplace popup
					!isSelectionMarker && // Click outside selection markers
					!isLinkPopup && // Click outside link input popup
					!isInsideToolbarButton &&// Click outside the toolbar button
					!isInsertButton &&
					!isInsideJoditPopupContent &&
					!isInsideAltTextPopup
				) {
					setIsEditing(false); // Close the editor if clicked outside
					setToolbarVisible(false); // Also hide toolbar when clicking outside
				}
			};

			if (isEditing) {
				document.addEventListener("mousedown", handleClickOutside);
				return () => document.removeEventListener("mousedown", handleClickOutside);
			}
		}, [isEditing]);

		return (
			<>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						position: "relative",
						//padding: 0,
						margin: 0,
						boxSizing: "border-box",
						transition: "border 0.2s ease-in-out",
						backgroundColor: sectionColor || "defaultColor",
						//border: `${BborderSize}px solid ${Bbordercolor} !important` || "defaultColor",
						// padding: `${bpadding}px !important` || "0",
					}}
					className="qadpt-rte"
					id="rte-box"
				>
					{/* RTE Container with Tooltip for action icons */}
					<Tooltip
						title={
							<>
								<IconButton
									size="small"
									onClick={() => handleRTECloneSection(id)}
									disabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3}
									title={toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? translate("Maximum limit of 3 Rich Text sections reached") : translate("Clone Section")}
									sx={{
										"&:hover": {
											backgroundColor: "transparent !important",
										},
										svg: {
											height: "24px",
											path: {
												fill:"var(--primarycolor)"
											}
										},
									}}
								>
									<span
										dangerouslySetInnerHTML={{ __html: copyicon }}
										style={{
											opacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length >= 3 ? 0.5 : 1,
											height: '24px'
										}}
									/>
								</IconButton>
								<IconButton
									size="small"
									onClick={() => handleRTEDeleteSection(id)}
									disabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}
									title={translate("Delete Section")}
									sx={{
										"&:hover": {
											backgroundColor: "transparent !important",
										},
										svg: {
											path: {
												fill:"var(--primarycolor)"
											}
										},
									}}
								>
									<span
										dangerouslySetInnerHTML={{ __html: deleteicon }}
										style={{
											opacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,
											height: '24px'
										}}
									/>
								</IconButton>
							</>
						}
						placement="top"
						slotProps={{
							tooltip: {
								sx: {
									backgroundColor: 'white',
									color: 'black',
									borderRadius: '4px',
									padding: '0px 4px',
									border: "1px dashed var(--primarycolor)",
									marginBottom: '30px !important'
								},
							},
						}}
						PopperProps={{
							modifiers: [
								{
									name: 'preventOverflow',
									options: { boundary: 'viewport' },
								},
								{
									name: 'flip',
									options: { enabled: true },
								},
							],
						}}
					>
						<div
							style={{
								width: "100%",
								position: "relative",
								color: "#000000",
								backgroundColor: "#ffffff"
							}}
							className="rte-container"
						>


							<style>
								{dynamicCSS}
							</style>
							<JoditEditor
								ref={editorRef}
								value={rteBoxValue || ""}
								config={joditConfig}
								onChange={handleContentChange}
							/>

							{/* Edit Icon - Always visible with dynamic positioning */}
							<IconButton
								size="small"
								onClick={(e) => {
									e.stopPropagation();
									toggleToolbar();
								}}
								sx={{
									position: "absolute",
									// Dynamic positioning: when toolbar is visible, always bottom-right
									bottom: toolbarVisible ? "8px" : (contentState.isEmpty ? "50%" : (contentState.isScrollable ? "8px" : "2px")),
									right: toolbarVisible ? "2px" : (contentState.isEmpty ? "auto" : "2px"),
									left: toolbarVisible ? "auto" : (contentState.isEmpty ? "calc(100% - 32px)" : "auto"),
									transform: toolbarVisible ? "none" : (contentState.isEmpty ? "translateY(50%)" : "none"),
									width: "24px",
									height: "24px",
									backgroundColor: "rgba(255, 255, 255, 0.9)",
									zIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top
									"&:hover": {
										backgroundColor: "rgba(255, 255, 255, 1)",
									},
									"& svg": {
										width: "16px",
										height: "16px",
									}
								}}
								title={translate("Toggle Toolbar")}
							>
								<span
									dangerouslySetInnerHTML={{ __html: editicon }}
									style={{ height: '16px', width: '16px' }}
								/>
							</IconButton>
						</div>
					</Tooltip>
				</Box>
			</>
		);
	}
);

export default memo(RTEsection);
