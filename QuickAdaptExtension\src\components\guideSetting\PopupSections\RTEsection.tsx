import React, { useState, useEffect, useRef, forwardRef,useMemo } from "react";
import { Box, TextField, Tooltip, IconButton } from "@mui/material";
import JoditEditor from "jodit-react";
import useDrawerStore from "../../../store/drawerStore";
import { copyicon, deleteicon, editicon } from "../../../assets/icons/icons";
import { selectedtemp } from "../../drawer/Drawer";
import { useTranslation } from 'react-i18next';

interface RTEsectionProps {
    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;
    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;
    isBanner: boolean;
    handleDeleteRTESection: (params: number) => void;
    index: number;
    onClone?: () => void;
    isCloneDisabled?: boolean;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {
        const { t: translate } = useTranslation();
        const {
            rtesContainer,
            updateRTEContainer,
            setIsUnSavedChanges,
            cloneRTEContainer,
            clearRteDetails,
            selectedTemplate,
            selectedTemplateTour,
            announcementGuideMetaData,
            toolTipGuideMetaData,
            handleAnnouncementRTEValue,
            handleTooltipRTEValue,
            createWithAI,
            currentStep,
            ensureAnnouncementRTEContainer
        } = useDrawerStore();

        // Individual state management for each RTE
        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);
        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);
        const [hoveredRTEId, setHoveredRTEId] = useState<string | null>(null);
        const contentRef = useRef<string>("");

        // State to track content for dynamic icon positioning
        const [contentStates, setContentStates] = useState<Map<string, { isEmpty: boolean, isScrollable: boolean }>>(new Map());

        // Map to store individual refs for each RTE
        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());
        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());

        // Helper function to get or create editor ref for specific RTE
        const getEditorRef = (rteId: string) => {
            if (!editorRefs.current.has(rteId)) {
                editorRefs.current.set(rteId, React.createRef());
            }
            return editorRefs.current.get(rteId);
        };

        // Helper function to get or create container ref for specific RTE
        const getContainerRef = (rteId: string) => {
            if (!containerRefs.current.has(rteId)) {
                containerRefs.current.set(rteId, React.createRef());
            }
            return containerRefs.current.get(rteId);
        };

        // Handle clicks outside the editor - now works with individual RTEs
        useEffect(() => {
            const handleClickOutside = (event: MouseEvent) => {
                if (!editingRTEId) return; // No RTE is currently being edited

                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
                const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
                const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
                const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
                const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
                const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
                const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
                const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

                // Get the container ref for the currently editing RTE
                const currentContainerRef = getContainerRef(editingRTEId);

                // Check if the target is inside the currently editing RTE or related elements
                if (
                    currentContainerRef?.current &&
                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container
                    !isInsidePopup && // Click outside the popup
                    !isInsideJoditPopup && // Click outside the WYSIWYG editor
                    !isInsideWorkplacePopup && // Click outside the workplace popup
                    !isSelectionMarker && // Click outside selection markers
                    !isLinkPopup && // Click outside link input popup
                    !isInsideToolbarButton && // Click outside the toolbar button
                    !isInsertButton &&
                    !isInsideJoditPopupContent &&
                    !isInsideAltTextPopup
                ) {
                    setEditingRTEId(null); // Close the currently editing RTE
                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside
                }
            };

            document.addEventListener("mousedown", handleClickOutside);
            return () => document.removeEventListener("mousedown", handleClickOutside);
        }, [editingRTEId]);

        useEffect(() => {
            if (editingRTEId) {
                const editorRef = getEditorRef(editingRTEId);
                if (editorRef?.current) {
                    setTimeout(() => {
                        //(editorRef.current as any).editor.focus();
                    }, 50);
                }
            }
        }, [editingRTEId]);

        // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)
        const isContentEmpty = (content: string): boolean => {
            if (!content) return true;
            // Remove HTML tags and check if there's actual text content
            const textContent = content.replace(/<[^>]*>/g, '').trim();
            return textContent.length === 0;
        };

        // Helper function to check if content is scrollable
        const isContentScrollable = (containerId: string): boolean => {
            const containerRef = getContainerRef(containerId);
            if (containerRef?.current) {
                const workplace = containerRef.current.querySelector('.jodit-workplace');
                if (workplace) {
                    return workplace.scrollHeight > workplace.clientHeight;
                }
            }
            return false;
        };

        // Update content state for dynamic icon positioning
        const updateContentState = (containerId: string, content: string) => {
            const isEmpty = isContentEmpty(content);
            const isScrollable = isContentScrollable(containerId);

            setContentStates(prev => {
                const newMap = new Map(prev);
                newMap.set(containerId, { isEmpty, isScrollable });
                return newMap;
            });
        };

        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {
            contentRef.current = newContent;

            // Check if this is an AI-created guide
            const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");
            const isAITour = createWithAI && selectedTemplate === "Tour";
            const isTourAnnouncement = isAITour && selectedTemplateTour === "Announcement";
            const isTourBanner = isAITour && selectedTemplateTour === "Banner";
            const isTourTooltip = isAITour && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot");

            console.log("RTEsection handleUpdate:", {
                createWithAI,
                selectedTemplate,
                selectedTemplateTour,
                isAIAnnouncement,
                isAITour,
                isTourBanner,
                containerId,
                newContent: newContent.substring(0, 50) + "..."
            });

            if (isAIAnnouncement) {
                const currentStepIndex = currentStep - 1;

                if (isTourAnnouncement) {
                    // For Tour+Announcement, use toolTipGuideMetaData
                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(
                        (container: any) => container.id === containerId && container.type === "rte"
                    );

                    if (tooltipContainer) {
                        // Use the tooltip-specific handler for tour announcements
                        handleTooltipRTEValue(containerId, newContent);
                    }
                } else {
                    // For pure Announcements, use announcementGuideMetaData
                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(
                        (container: any) => container.id === containerId && container.type === "rte"
                    );

                    if (announcementContainer) {
                        // Use the announcement-specific handler
                        handleAnnouncementRTEValue(containerId, newContent);
                    }
                }
            } else if (isAITour && (isTourBanner || isTourTooltip)) {
                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData
                const currentStepIndex = currentStep - 1;
                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(
                    (container: any) => container.id === containerId && container.type === "rte"
                );

                if (tooltipContainer) {
                    // Use the tooltip-specific handler for all tour step types
                    handleTooltipRTEValue(containerId, newContent);
                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);
                } else {
                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {
                        currentStepIndex,
                        containerId,
                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))
                    });
                }
            } else {
                // For non-AI content or other cases, use the regular RTE container system
                updateRTEContainer(containerId, rteId, newContent);
                console.log("Used updateRTEContainer for non-AI content");
            }

            setIsUnSavedChanges(true);

            // Update content state for dynamic icon positioning
            updateContentState(containerId, newContent);
        };
        const handleCloneContainer = (containerId: string) => {
            // Check if cloning is disabled due to section limits
            if (isCloneDisabled) {
                return; // Don't clone if limit is reached
            }

            // Call the clone function from the store
            cloneRTEContainer(containerId);

            // Call the onClone callback if provided
            if (onClone) {
                onClone();
            }
        };
        const handleDeleteSection = (containerId: string, rteId:string) => {
            // Check if this is an AI-created announcement
            const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");

            if (isAIAnnouncement) {
                // For AI announcements, we need to remove from announcementGuideMetaData
                // This would require a new function in the store, for now just call the existing one
                clearRteDetails(containerId, rteId);
            } else {
                // For banners and non-AI content, use the regular clear function
                clearRteDetails(containerId, rteId);
            }

            // Call the handleDeleteRTESection callback to update section counts
            handleDeleteRTESection(index);
        };
        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {
            event.preventDefault();

            const clipboardData = event.clipboardData;
            const pastedText = clipboardData.getData("text/plain");
            const pastedHtml = clipboardData.getData("text/html");

            if (pastedHtml) {
                const isRTEContent = pastedHtml.includes("<!--RTE-->");
                if (isRTEContent) {
                    insertContent(pastedHtml);
                } else {
                    insertContent(pastedHtml);
                }
            } else {
                insertContent(pastedText);
            }
        };


        const insertContent = (content: string) => {
            if (editingRTEId) {
                const editorRef = getEditorRef(editingRTEId);
                if (editorRef?.current) {
                    const editor = (editorRef.current as any).editor;
                    editor.selection.insertHTML(content);
                }
            }
        };

        const toggleToolbar = (rteId: string) => {
            if (toolbarVisibleRTEId === rteId) {
                setToolbarVisibleRTEId(null);
            } else {
                setToolbarVisibleRTEId(rteId);
                // Don't set editing state, just show toolbar
            }
        };
        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);
        useEffect(() => {
    const dir = document.body.getAttribute("dir") || "ltr";
    setIsRtlDirection(dir.toLowerCase() === "rtl");
}, []);
    const config = useMemo(
        (): any => ({
            readonly: false, // all options from https://xdsoft.net/jodit/docs/,
            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,

// Jodit uses 'direction' not just 'rtl'
        language:  'en', // Optional: change language as well
            toolbarSticky: false,
            toolbarAdaptive: false,
            // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state
            toolbar: toolbarVisibleRTEId !== null,
            // Enhanced height and scrolling behavior
            height: 'auto',
            minHeight: toolbarVisibleRTEId !== null ? 130 : 28, // 130px when toolbar visible, 28px when hidden
            maxHeight: selectedTemplate === "Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? 'auto' : 150,
            buttons: [

        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
        'font', 'fontsize', 'link',
        {
            name: 'more',
            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
            list: [
                        'source',
                        'image', 'video', 'table',
                'align', 'undo', 'redo', '|',
                'hr', 'eraser', 'copyformat',
                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',
                'outdent', 'indent', 'paragraph',
            ]
        }
    ],
            autofocus: true,
      showCharsCounter: false,
			showWordsCounter: false,
			showXPathInStatusbar: false,
			statusbar: false,
            pastePlain: true,
			askBeforePasteHTML: false,
			askBeforePasteFromWord: false,
    // Fix dialog positioning by setting popup root to document body
    popupRoot: document.body,
    // Fix popup positioning issues
    zIndex: 100000,
    globalFullSize: false,
    // Fix link dialog positioning
    link: {
        followOnDblClick: false,
        processVideoLink: true,
        processPastedLink: true,
        openInNewTabCheckbox: true,
        noFollowCheckbox: false,
        modeClassName: 'input' as const,
    },
    // Dialog configuration
    dialog: {
        zIndex: 100001,
    },
    cursorAfterAutofocus: 'end' as const,
    events: {
                onPaste: handlePaste, // Attach custom onPaste handler
    },
    controls: {
        font: {
            list: {
                "Poppins, sans-serif": "Poppins",
                "Roboto, sans-serif": "Roboto",
                "Comic Sans MS, sans-serif": "Comic Sans MS",
                "Open Sans, sans-serif": "Open Sans",
                "Calibri, sans-serif": "Calibri",
                "Century Gothic, sans-serif": "Century Gothic",
            }
        }
            }
    }),[isRtlDirection, toolbarVisibleRTEId]

    );

        // Determine which containers to use based on guide type
        const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");
        const isAITour = createWithAI && selectedTemplate === "Tour";
        const isTourAnnouncement = isAITour && selectedTemplateTour === "Announcement";
        const isTourBanner = isAITour && selectedTemplateTour === "Banner";
        const isTourTooltip = isAITour && (selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot");
        const currentStepIndex = currentStep - 1;

        let containersToRender: any[] = [];

        if (isAIAnnouncement && !isTourAnnouncement) {
            // For pure AI announcements (not in tours), use announcementGuideMetaData
            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);
        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {
            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData
            if (toolTipGuideMetaData[currentStepIndex]?.containers) {
                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === "rte");
                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {
                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,
                    rteContainers: containersToRender.length,
                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))
                });
            } else {
                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);
                containersToRender = [];
            }
        } else {
            // For non-AI content, use rtesContainer
            containersToRender = rtesContainer;
        }

        return (
            <>
                {containersToRender.map((item: any) => {
                    let rteText = "";
                    let rteId = "";
                    let id = "";

                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {
                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container
                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers
                        rteText = item.rteBoxValue || "";
                        rteId = item.id;
                        id = item.id;
                    } else {
                        // For non-AI content, get data from rtesContainer
                        rteText = item.rtes?.[0]?.text || "";
                        rteId = item.rtes?.[0]?.id;
                        id = item.id;
                    }

                    if (!id) return null;


                    const currentContainerRef = getContainerRef(id);
                    const currentEditorRef = getEditorRef(id);

                    // Get content state for dynamic icon positioning
                    const contentState = contentStates.get(id) || { isEmpty: isContentEmpty(rteText), isScrollable: false };

                    return (
                        <Box
                            key={id}
                            ref={currentContainerRef}
                            sx={{
                                position: "relative",
                                width: "100%",
                                "& .jodit-add-new-line": {
											display: "none !important",
										},
                                "& .jodit-status-bar-link": {
                                    display: "none !important",
                                },
                                "& .jodit-editor": {
                                    fontFamily: "'Roboto', sans-serif !important",
                                },
                                ".jodit-editor span": {
                                    fontFamily: "'Roboto', sans-serif !important",
                                },
                                ".jodit-toolbar-button button": {
                                    minWidth: "29px !important",
                                },
                                ".jodit-react-container": {
                                    width: selectedTemplate === "Banner" ? "100%" : "100%",
                                    whiteSpace: "pre-wrap",
                                    wordBreak: "break-word",
                                },
                                ".jodit-workplace": {
                                    minHeight: toolbarVisibleRTEId !== null ? "130px !important" : (selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "50px !important": "28px !important"),
                                    maxHeight: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "50px !important" : "150px !important",
                                    overflow: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ?"hidden" : "auto !important",
                                },
                                ".jodit-container": {
                                    border: "none !important"
                                },
                                ".jodit-toolbar__box": {
                                    display: "flex !important",
                                    justifyContent: "center !important",
                                    height: selectedTemplate==="Banner" || (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "32px !important": null,
                                    maxHeight: selectedTemplate==="Banner"|| (selectedTemplate === "Tour" && selectedTemplateTour === "Banner") ? "32px !important": null
                                },
                                // Fix Jodit dialog positioning - target correct classes
                                ".jodit.jodit-dialog": {
                                    position: "fixed !important",
                                    zIndex: "100001 !important",
                                    top: "50% !important",
                                    left: "50% !important",
                                    transform: "translate(-50%, -50%) !important"
                                },
                                ".jodit-dialog .jodit-dialog__panel": {
                                    position: "relative !important",
                                    top: "auto !important",
                                    left: "auto !important",
                                    transform: "none !important",
                                    maxWidth: "400px !important",
                                    background: "white !important",
                                    border: "1px solid #ccc !important",
                                    borderRadius: "4px !important",
                                    boxShadow: "0 4px 12px rgba(0,0,0,0.15) !important"
                                },
                                // Fix for link dialog specifically
                                ".jodit-dialog_alert": {
                                    position: "fixed !important",
                                    zIndex: "100001 !important",
                                    top: "50% !important",
                                    left: "50% !important",
                                    transform: "translate(-50%, -50%) !important"
                                },
                                // Enhanced scrollbar styling for consistent behavior
                                ".jodit-workplace::-webkit-scrollbar": {
                                    width: "6px !important",
                                },
                                ".jodit-workplace::-webkit-scrollbar-track": {
                                    background: "#f1f1f1 !important",
                                    borderRadius: "3px !important",
                                },
                                ".jodit-workplace::-webkit-scrollbar-thumb": {
                                    background: "#c1c1c1 !important",
                                    borderRadius: "3px !important",
                                },
                                ".jodit-workplace::-webkit-scrollbar-thumb:hover": {
                                    background: "#a8a8a8 !important",
                                },
                                // Ensure proper line height and text styling
                                ".jodit-wysiwyg": {
                                    lineHeight: "1.4 !important",
                                    padding: "8px !important",
                                },
                                ".jodit-wysiwyg p": {
                                    margin: "0 0 4px 0 !important",
                                    lineHeight: "1.4 !important",
                                },
                                "&:hover .rte-action-icons": {
                                    opacity: "1 !important",
                                    visibility: "visible !important"
                                },
                                "&.qadpt-rte:hover .rte-action-icons": {
                                    opacity: "1 !important",
                                    visibility: "visible !important"
                                }
                            }}
                            className="qadpt-rte"
                            onMouseEnter={() => setHoveredRTEId(id)}
                            onMouseLeave={() => setHoveredRTEId(null)}
                        >
                            {/* Always show Jodit editor, but conditionally show tooltips for non-Banner templates */}
                            {(selectedTemplate === "Announcement" || selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot") || (selectedTemplateTour === "Announcement" || selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot") ? (
                                <>
                                    {/* Clone and Delete Icons positioned at top-right corner */}
                                    <div
                                        style={{
                                            position: "absolute",
                                            // Complex positioning logic:
                                            // 1. When toolbar visible: top-right of jodit-workplace (below toolbar)
                                            // 2. When no content: side by side with edit icon (clone, delete, edit order)
                                            // 3. When has content: top-right corner
                                            top: toolbarVisibleRTEId === id ? "45px" : (contentState.isEmpty ? "50%" : "8px"),
                                            right: toolbarVisibleRTEId === id ? "8px" : (contentState.isEmpty ? "calc(50% - 198px)" : "8px"),
                                            left: toolbarVisibleRTEId === id ? "auto" : (contentState.isEmpty ? "auto" : "auto"),
                                            transform: toolbarVisibleRTEId === id ? "none" : (contentState.isEmpty ? "translateY(-50%)" : "none"),
                                            display: "flex",
                                            gap: "4px",
                                            zIndex: 1003,
                                            opacity: hoveredRTEId === id ? 1 : 0,
                                            visibility: hoveredRTEId === id ? "visible" : "hidden",
                                            transition: "opacity 0.2s ease-in-out, visibility 0.2s ease-in-out",
                                            pointerEvents: "auto"
                                        }}
                                        className="rte-action-icons"
                                    >
                                        {/* Clone Icon */}
                                        <IconButton
                                            size="small"
                                            onClick={() => handleCloneContainer(item.id)}
                                            disabled={isCloneDisabled}
                                            title={isCloneDisabled ? translate("Maximum limit of 3 Rich Text sections reached") : translate("Clone Section")}
                                            sx={{
                                                width: "24px",
                                                height: "24px",
                                                backgroundColor: "rgba(255, 255, 255, 0.95)",
                                                border: "1px solid #e0e0e0",
                                                borderRadius: "4px",
                                                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 255, 255, 1)",
                                                    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.15)",
                                                    transform: "scale(1.05)",
                                                },
                                                "&:disabled": {
                                                    opacity: 0.5,
                                                    cursor: "not-allowed"
                                                },
                                                transition: "all 0.2s ease-in-out",
                                                svg: {
                                                    height: "16px",
                                                    width: "16px",
                                                    path: {
                                                        fill: "var(--primarycolor)"
                                                    }
                                                },
                                            }}
                                        >
                                            <span
                                                dangerouslySetInnerHTML={{ __html: copyicon }}
                                                style={{
                                                    height: '16px',
                                                    width: '16px'
                                                }}
                                            />
                                        </IconButton>

                                        {/* Delete Icon */}
                                        <IconButton
                                            size="small"
                                            onClick={() => handleDeleteSection(item.id, rteId)}
                                            title={translate("Delete Section")}
                                            sx={{
                                                width: "24px",
                                                height: "24px",
                                                backgroundColor: "rgba(255, 255, 255, 0.95)",
                                                border: "1px solid #e0e0e0",
                                                borderRadius: "4px",
                                                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 245, 245, 1) !important",
                                                    borderColor: "#ff4444 !important",
                                                    boxShadow: "0 4px 8px rgba(255, 68, 68, 0.2) !important",
                                                    transform: "scale(1.05) !important",
                                                    zIndex: "1003 !important"
                                                },
                                                "&:hover span": {
                                                    filter: "brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%) !important"
                                                },
                                                transition: "all 0.2s ease-in-out",
                                                zIndex: 1003,
                                                svg: {
                                                    height: "16px",
                                                    width: "16px",
                                                    "& path": {
                                                        fill: "#ff4444 !important"
                                                    },
                                                    "& g path": {
                                                        fill: "#ff4444 !important"
                                                    }
                                                },
                                            }}
                                        >
                                            <span
                                                style={{
                                                    height: '16px',
                                                    width: '16px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    color: '#ff4444',
                                                    fontSize: '16px',
                                                    fontWeight: 'bold'
                                                }}
                                            >
                                                🗑️
                                            </span>
                                        </IconButton>
                                    </div>

                                    <div style={{ width: "100%", position: "relative" }}>
                                        <JoditEditor
                                            ref={currentEditorRef}
                                            value={rteText}
                                            config={config}
                                            onChange={(newContent) => handleUpdate(newContent, rteId, id)}
                                        />
                                        {/* Edit icon for toolbar toggle - positioned at bottom right */}
                                        <IconButton
                                            size="small"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                toggleToolbar(id);
                                            }}
                                            sx={{
                                                position: "absolute",
                                                // Dynamic positioning: when toolbar is visible, always bottom-right
                                                bottom: toolbarVisibleRTEId !== null ? "8px" : (contentState.isEmpty ? "50%" : (contentState.isScrollable ? "8px" : "2px")),
                                                right: toolbarVisibleRTEId !== null ? "2px" : (contentState.isEmpty ? "auto" : "2px"),
                                                left: toolbarVisibleRTEId !== null ? "auto" : (contentState.isEmpty ? "calc(100% - 32px)" : "auto"),
                                                transform: toolbarVisibleRTEId !== null ? "none" : (contentState.isEmpty ? "translateY(50%)" : "none"),
                                                width: "24px",
                                                height: "24px",
                                                backgroundColor: "rgba(255, 255, 255, 0.9)",
                                                zIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 255, 255, 1)",
                                                },
                                                "& svg": {
                                                    width: "16px",
                                                    height: "16px",
                                                }
                                            }}
                                            title={translate("Toggle Toolbar")}
                                        >
                                            <span
                                                dangerouslySetInnerHTML={{ __html: editicon }}
                                                style={{ height: '16px', width: '16px' }}
                                            />
                                        </IconButton>
                                    </div>
                                </>
                            ) : (
                                <div style={{ width: "100%", position: "relative" }}>
                                    <JoditEditor
                                        ref={currentEditorRef}
                                        value={rteText}
                                        config={config}
                                        onChange={(newContent) => handleUpdate(newContent, rteId, id)}
                                    />
                                    {/* Edit icon for toolbar toggle - positioned at bottom right */}
                                    <IconButton
                                        size="small"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toggleToolbar(id);
                                        }}
                                        sx={{
                                            position: "absolute",
                                            // Dynamic positioning: when toolbar is visible, always bottom-right
                                            bottom: toolbarVisibleRTEId !== null ? "8px" : (contentState.isEmpty ? "50%" : (contentState.isScrollable ? "8px" : "2px")),
                                            right: toolbarVisibleRTEId !== null ? "2px" : (contentState.isEmpty ? "auto" : "2px"),
                                            left: toolbarVisibleRTEId !== null ? "auto" : (contentState.isEmpty ? "calc(100% - 32px)" : "auto"),
                                            transform: toolbarVisibleRTEId !== null ? "none" : (contentState.isEmpty ? "translateY(50%)" : "none"),
                                            width: "24px",
                                            height: "24px",
                                            backgroundColor: "rgba(255, 255, 255, 0.9)",
                                            zIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top
                                            "&:hover": {
                                                backgroundColor: "rgba(255, 255, 255, 1)",
                                            },
                                            "& svg": {
                                                width: "16px",
                                                height: "16px",
                                            }
                                        }}
                                        title={translate("Toggle Toolbar")}
                                    >
                                        <span
                                            dangerouslySetInnerHTML={{ __html: editicon }}
                                            style={{ height: '16px', width: '16px' }}
                                        />
                                    </IconButton>
                                </div>
                            )}
                        </Box>
                    );
                })}
            </>
        );
    }
);

export default RTEsection;
