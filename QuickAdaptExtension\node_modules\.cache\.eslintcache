[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "94"}, {"size": 604, "mtime": 1748929949904, "results": "95", "hashOfConfig": "96"}, {"size": 440, "mtime": 1748929949904, "results": "97", "hashOfConfig": "96"}, {"size": 2871, "mtime": 1753273113372, "results": "98", "hashOfConfig": "96"}, {"size": 3344, "mtime": 1753158986544, "results": "99", "hashOfConfig": "96"}, {"size": 244157, "mtime": 1753273113716, "results": "100", "hashOfConfig": "96"}, {"size": 6890, "mtime": 1753072098544, "results": "101", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1753072098560, "results": "102", "hashOfConfig": "96"}, {"size": 3112, "mtime": 1753072087888, "results": "103", "hashOfConfig": "96"}, {"size": 395822, "mtime": 1753335578612, "results": "104", "hashOfConfig": "96"}, {"size": 3927, "mtime": 1748930023076, "results": "105", "hashOfConfig": "96"}, {"size": 6144, "mtime": 1749531263982, "results": "106", "hashOfConfig": "96"}, {"size": 41357, "mtime": 1753273113728, "results": "107", "hashOfConfig": "96"}, {"size": 5077, "mtime": 1753273113730, "results": "108", "hashOfConfig": "96"}, {"size": 3731, "mtime": 1753270938486, "results": "109", "hashOfConfig": "96"}, {"size": 13824, "mtime": 1753273113748, "results": "110", "hashOfConfig": "96"}, {"size": 13085, "mtime": 1753273113748, "results": "111", "hashOfConfig": "96"}, {"size": 28388, "mtime": 1753072098575, "results": "112", "hashOfConfig": "96"}, {"size": 1898, "mtime": 1748930023076, "results": "113", "hashOfConfig": "96"}, {"size": 1954, "mtime": 1751432283612, "results": "114", "hashOfConfig": "96"}, {"size": 9087, "mtime": 1753158986542, "results": "115", "hashOfConfig": "96"}, {"size": 296570, "mtime": 1753072087794, "results": "116", "hashOfConfig": "96"}, {"size": 193, "mtime": 1748929949654, "results": "117", "hashOfConfig": "96"}, {"size": 9097, "mtime": 1753158986506, "results": "118", "hashOfConfig": "96"}, {"size": 30700, "mtime": 1753072098544, "results": "119", "hashOfConfig": "96"}, {"size": 3012, "mtime": 1753273113704, "results": "120", "hashOfConfig": "96"}, {"size": 2606, "mtime": 1753072098528, "results": "121", "hashOfConfig": "96"}, {"size": 33243, "mtime": 1753273113725, "results": "122", "hashOfConfig": "96"}, {"size": 23270, "mtime": 1753273113720, "results": "123", "hashOfConfig": "96"}, {"size": 13556, "mtime": 1753072087810, "results": "124", "hashOfConfig": "96"}, {"size": 26724, "mtime": 1753273113388, "results": "125", "hashOfConfig": "96"}, {"size": 49705, "mtime": 1751532053846, "results": "126", "hashOfConfig": "96"}, {"size": 7599, "mtime": 1753072087888, "results": "127", "hashOfConfig": "96"}, {"size": 30037, "mtime": 1753158986499, "results": "128", "hashOfConfig": "96"}, {"size": 11669, "mtime": 1753273113763, "results": "129", "hashOfConfig": "96"}, {"size": 24200, "mtime": 1751432283612, "results": "130", "hashOfConfig": "96"}, {"size": 4880, "mtime": 1750229130169, "results": "131", "hashOfConfig": "96"}, {"size": 9238, "mtime": 1748930023061, "results": "132", "hashOfConfig": "96"}, {"size": 1297, "mtime": 1748930023061, "results": "133", "hashOfConfig": "96"}, {"size": 1248, "mtime": 1748929949920, "results": "134", "hashOfConfig": "96"}, {"size": 14238, "mtime": 1748930023076, "results": "135", "hashOfConfig": "96"}, {"size": 2997, "mtime": 1753072087872, "results": "136", "hashOfConfig": "96"}, {"size": 3285, "mtime": 1753273113732, "results": "137", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1753273113707, "results": "138", "hashOfConfig": "96"}, {"size": 955, "mtime": 1753072098528, "results": "139", "hashOfConfig": "96"}, {"size": 19907, "mtime": 1753072098560, "results": "140", "hashOfConfig": "96"}, {"size": 743, "mtime": 1748929949654, "results": "141", "hashOfConfig": "96"}, {"size": 25466, "mtime": 1753273113732, "results": "142", "hashOfConfig": "96"}, {"size": 2608, "mtime": 1748930023061, "results": "143", "hashOfConfig": "96"}, {"size": 38527, "mtime": 1753335588191, "results": "144", "hashOfConfig": "96"}, {"size": 7772, "mtime": 1753072087872, "results": "145", "hashOfConfig": "96"}, {"size": 16105, "mtime": 1753273113732, "results": "146", "hashOfConfig": "96"}, {"size": 29119, "mtime": 1753158986510, "results": "147", "hashOfConfig": "96"}, {"size": 6245, "mtime": 1748929949857, "results": "148", "hashOfConfig": "96"}, {"size": 2034, "mtime": 1753072098528, "results": "149", "hashOfConfig": "96"}, {"size": 29744, "mtime": 1753158986497, "results": "150", "hashOfConfig": "96"}, {"size": 1962, "mtime": 1748929949654, "results": "151", "hashOfConfig": "96"}, {"size": 27258, "mtime": 1753273113679, "results": "152", "hashOfConfig": "96"}, {"size": 2401, "mtime": 1753248412085, "results": "153", "hashOfConfig": "96"}, {"size": 702, "mtime": 1753072087841, "results": "154", "hashOfConfig": "96"}, {"size": 13889, "mtime": 1753072087841, "results": "155", "hashOfConfig": "96"}, {"size": 19040, "mtime": 1753072087856, "results": "156", "hashOfConfig": "96"}, {"size": 6625, "mtime": 1753072087872, "results": "157", "hashOfConfig": "96"}, {"size": 20321, "mtime": 1753072087872, "results": "158", "hashOfConfig": "96"}, {"size": 3236, "mtime": 1748929949779, "results": "159", "hashOfConfig": "96"}, {"size": 2848, "mtime": 1748929949811, "results": "160", "hashOfConfig": "96"}, {"size": 15285, "mtime": 1753072087825, "results": "161", "hashOfConfig": "96"}, {"size": 15261, "mtime": 1753158986514, "results": "162", "hashOfConfig": "96"}, {"size": 11208, "mtime": 1753072087856, "results": "163", "hashOfConfig": "96"}, {"size": 17207, "mtime": 1753158986529, "results": "164", "hashOfConfig": "96"}, {"size": 8476, "mtime": 1753072087856, "results": "165", "hashOfConfig": "96"}, {"size": 15571, "mtime": 1753072098560, "results": "166", "hashOfConfig": "96"}, {"size": 16126, "mtime": 1749557445376, "results": "167", "hashOfConfig": "96"}, {"size": 33226, "mtime": 1753273113403, "results": "168", "hashOfConfig": "96"}, {"size": 60407, "mtime": 1753072087810, "results": "169", "hashOfConfig": "96"}, {"size": 26698, "mtime": 1753158986504, "results": "170", "hashOfConfig": "96"}, {"size": 5258, "mtime": 1753072087872, "results": "171", "hashOfConfig": "96"}, {"size": 883, "mtime": 1748929949889, "results": "172", "hashOfConfig": "96"}, {"size": 3117, "mtime": 1753273113699, "results": "173", "hashOfConfig": "96"}, {"size": 7943, "mtime": 1748930023061, "results": "174", "hashOfConfig": "96"}, {"size": 491, "mtime": 1751432283612, "results": "175", "hashOfConfig": "96"}, {"size": 5504, "mtime": 1753072087841, "results": "176", "hashOfConfig": "96"}, {"size": 33137, "mtime": 1753273113673, "results": "177", "hashOfConfig": "96"}, {"size": 37236, "mtime": 1753273113667, "results": "178", "hashOfConfig": "96"}, {"size": 2931, "mtime": 1749010760558, "results": "179", "hashOfConfig": "96"}, {"size": 2669, "mtime": 1748929949748, "results": "180", "hashOfConfig": "96"}, {"size": 17277, "mtime": 1753273113497, "results": "181", "hashOfConfig": "96"}, {"size": 27631, "mtime": 1753273113575, "results": "182", "hashOfConfig": "96"}, {"size": 16671, "mtime": 1753335588191, "results": "183", "hashOfConfig": "96"}, {"size": 15436, "mtime": 1753158986501, "results": "184", "hashOfConfig": "96"}, {"size": 677, "mtime": 1753072098575, "results": "185", "hashOfConfig": "96"}, {"size": 6886, "mtime": 1753158986540, "results": "186", "hashOfConfig": "96"}, {"size": 7158, "mtime": 1753158986535, "results": "187", "hashOfConfig": "96"}, {"size": 9356, "mtime": 1753273113693, "results": "188", "hashOfConfig": "96"}, {"size": 1211, "mtime": 1753158986538, "results": "189", "hashOfConfig": "96"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 224, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["472", "473", "474"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["475"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["700", "701", "702", "703", "704", "705"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["706", "707"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["728"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["729"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["770"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["811", "812", "813", "814"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["815", "816", "817"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["818", "819", "820", "821", "822", "823", "824", "825"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["853", "854", "855", "856"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1118", "1119", "1120", "1121", "1122", "1123"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1124", "1125", "1126", "1127"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1128", "1129"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1130", "1131", "1132"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1133", "1134"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1135"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1136", "1137", "1138"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1151", "1152", "1153"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1154", "1155", "1156", "1157"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1196", "1197", "1198", "1199", "1200"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1326", "1327", "1328", "1329"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1330", "1331", "1332", "1333", "1334", "1335"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1356", "1357", "1358"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1359", "1360"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1638", "1639"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1804", "1805"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1869"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1870", "1871", "1872"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1873", "1874"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1875"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], {"ruleId": "1876", "severity": 1, "message": "1877", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1880", "line": 9, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "1881", "line": 16, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1882", "line": 1, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "1883", "line": 1, "column": 58, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 65}, {"ruleId": "1876", "severity": 1, "message": "1884", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1885", "line": 6, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1886", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "1887", "line": 12, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 40}, {"ruleId": "1876", "severity": 1, "message": "1888", "line": 17, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "1889", "line": 22, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1890", "line": 23, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1891", "line": 24, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1892", "line": 25, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1893", "line": 26, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1894", "line": 27, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1895", "line": 28, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1896", "line": 29, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "1897", "line": 30, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 30, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1898", "line": 31, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1899", "line": 32, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 32, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1900", "line": 33, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 33, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1901", "line": 34, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1902", "line": 35, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 35, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1903", "line": 37, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 37, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1904", "line": 38, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1905", "line": 39, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1906", "line": 40, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 44, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1908", "line": 50, "column": 20, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 60, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1910", "line": 61, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1911", "line": 68, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 68, "endColumn": 6}, {"ruleId": "1876", "severity": 1, "message": "1912", "line": 69, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 69, "endColumn": 6}, {"ruleId": "1876", "severity": 1, "message": "1913", "line": 76, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 76, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "1914", "line": 78, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 78, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1915", "line": 79, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 79, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "1916", "line": 79, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 79, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "1917", "line": 82, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "1918", "line": 83, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 83, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1919", "line": 84, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 84, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1920", "line": 88, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 88, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1921", "line": 90, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 90, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "1922", "line": 96, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 96, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1923", "line": 103, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 103, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1924", "line": 106, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 106, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1925", "line": 107, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "1926", "line": 112, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 112, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1927", "line": 112, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 112, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "1928", "line": 115, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 115, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1929", "line": 122, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 122, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1930", "line": 135, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 135, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1931", "line": 198, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 198, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1932", "line": 215, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 215, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1933", "line": 223, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 223, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1934", "line": 379, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 379, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1935", "line": 414, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 414, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1936", "line": 416, "column": 6, "nodeType": "1878", "messageId": "1879", "endLine": 416, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "1937", "line": 418, "column": 6, "nodeType": "1878", "messageId": "1879", "endLine": 418, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "1938", "line": 434, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 434, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1939", "line": 435, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 435, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "1940", "line": 437, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 437, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "1941", "line": 440, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 440, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "1942", "line": 444, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 444, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "1943", "line": 445, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 445, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "1944", "line": 456, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 456, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1945", "line": 457, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 457, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1946", "line": 458, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 458, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1947", "line": 460, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 460, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1948", "line": 460, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 460, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "1949", "line": 465, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 465, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "1950", "line": 465, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 465, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "1951", "line": 467, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 467, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1952", "line": 467, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 467, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "1953", "line": 470, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 470, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1954", "line": 470, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 470, "endColumn": 40}, {"ruleId": "1876", "severity": 1, "message": "1955", "line": 471, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 471, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "1956", "line": 476, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 476, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "1957", "line": 476, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 476, "endColumn": 50}, {"ruleId": "1876", "severity": 1, "message": "1958", "line": 483, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 483, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1959", "line": 483, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 483, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1960", "line": 485, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 485, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "1961", "line": 485, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 485, "endColumn": 41}, {"ruleId": "1876", "severity": 1, "message": "1962", "line": 487, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 487, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1963", "line": 487, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 487, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1964", "line": 500, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 500, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "1965", "line": 501, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 501, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "1966", "line": 501, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 501, "endColumn": 58}, {"ruleId": "1876", "severity": 1, "message": "1967", "line": 504, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 504, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1968", "line": 504, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 504, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "1969", "line": 505, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 505, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1970", "line": 505, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 505, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1971", "line": 506, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 506, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1972", "line": 506, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 506, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "1973", "line": 515, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 515, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "1974", "line": 516, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 516, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1975", "line": 522, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 522, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1976", "line": 526, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 526, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1977", "line": 526, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 526, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "1978", "line": 529, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 529, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1979", "line": 529, "column": 20, "nodeType": "1878", "messageId": "1879", "endLine": 529, "endColumn": 32}, {"ruleId": "1980", "severity": 1, "message": "1981", "line": 569, "column": 5, "nodeType": "1982", "endLine": 569, "endColumn": 27, "suggestions": "1983"}, {"ruleId": "1876", "severity": 1, "message": "1984", "line": 579, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 579, "endColumn": 6}, {"ruleId": "1876", "severity": 1, "message": "1985", "line": 580, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 580, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 581, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 581, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1987", "line": 583, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 583, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1988", "line": 584, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 584, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1989", "line": 589, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 589, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1990", "line": 590, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 590, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "1991", "line": 625, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 625, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1992", "line": 626, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 626, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1993", "line": 627, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 627, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1994", "line": 635, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 635, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 637, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 637, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1996", "line": 638, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 638, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1997", "line": 639, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 639, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1998", "line": 640, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 640, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1999", "line": 645, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 645, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2000", "line": 647, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 647, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2001", "line": 649, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 649, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2002", "line": 659, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 659, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2003", "line": 660, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 660, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2004", "line": 663, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 663, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2005", "line": 667, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 667, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2006", "line": 669, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 669, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2007", "line": 670, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 670, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2008", "line": 672, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 672, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2009", "line": 679, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 679, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2010", "line": 680, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 680, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2011", "line": 685, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 685, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2012", "line": 686, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 686, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2013", "line": 687, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 687, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2014", "line": 697, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 697, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2015", "line": 701, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 701, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2016", "line": 705, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 705, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2017", "line": 707, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 707, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2018", "line": 709, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 709, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2019", "line": 710, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 710, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2020", "line": 715, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 715, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2021", "line": 716, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 716, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2022", "line": 727, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 727, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2023", "line": 734, "column": 18, "nodeType": "1878", "messageId": "1879", "endLine": 734, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2024", "line": 735, "column": 18, "nodeType": "1878", "messageId": "1879", "endLine": 735, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2025", "line": 739, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 739, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2026", "line": 751, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 751, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2027", "line": 776, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 776, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2028", "line": 787, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 787, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2029", "line": 792, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 792, "endColumn": 42}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 797, "column": 22, "nodeType": "2032", "messageId": "2033", "endLine": 797, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2034", "line": 883, "column": 5, "nodeType": "1982", "endLine": 883, "endColumn": 46, "suggestions": "2035"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 883, "column": 6, "nodeType": "2037", "endLine": 883, "endColumn": 29}, {"ruleId": "1980", "severity": 1, "message": "2038", "line": 901, "column": 5, "nodeType": "1982", "endLine": 901, "endColumn": 18, "suggestions": "2039"}, {"ruleId": "1876", "severity": 1, "message": "2040", "line": 903, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 903, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2041", "line": 904, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 904, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2042", "line": 925, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 925, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2043", "line": 949, "column": 8, "nodeType": "2044", "endLine": 951, "endColumn": 3}, {"ruleId": "1980", "severity": 1, "message": "2045", "line": 986, "column": 5, "nodeType": "1982", "endLine": 994, "endColumn": 3, "suggestions": "2046"}, {"ruleId": "1980", "severity": 1, "message": "2047", "line": 1022, "column": 5, "nodeType": "1982", "endLine": 1045, "endColumn": 3, "suggestions": "2048"}, {"ruleId": "1980", "severity": 1, "message": "2049", "line": 1163, "column": 5, "nodeType": "1982", "endLine": 1163, "endColumn": 39, "suggestions": "2050"}, {"ruleId": "1876", "severity": 1, "message": "2051", "line": 1282, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 1282, "endColumn": 24}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1373, "column": 25, "nodeType": "2032", "messageId": "2033", "endLine": 1373, "endColumn": 27}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1380, "column": 25, "nodeType": "2032", "messageId": "2033", "endLine": 1380, "endColumn": 27}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1380, "column": 53, "nodeType": "2032", "messageId": "2033", "endLine": 1380, "endColumn": 55}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1383, "column": 26, "nodeType": "2032", "messageId": "2033", "endLine": 1383, "endColumn": 28}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1383, "column": 58, "nodeType": "2032", "messageId": "2033", "endLine": 1383, "endColumn": 60}, {"ruleId": "1876", "severity": 1, "message": "2053", "line": 1514, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1514, "endColumn": 33}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 1591, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 1591, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2054", "line": 1738, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1738, "endColumn": 30}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 2000, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 2000, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 2172, "column": 25, "nodeType": "2032", "messageId": "2033", "endLine": 2172, "endColumn": 27}, {"ruleId": "1980", "severity": 1, "message": "2055", "line": 2204, "column": 5, "nodeType": "1982", "endLine": 2204, "endColumn": 18, "suggestions": "2056"}, {"ruleId": "1876", "severity": 1, "message": "2057", "line": 2261, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 2261, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "2058", "line": 2272, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 2272, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2059", "line": 2272, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 2272, "endColumn": 48}, {"ruleId": "1876", "severity": 1, "message": "2060", "line": 2664, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 2664, "endColumn": 27}, {"ruleId": "1980", "severity": 1, "message": "2061", "line": 2699, "column": 5, "nodeType": "1982", "endLine": 2699, "endColumn": 38, "suggestions": "2062"}, {"ruleId": "1876", "severity": 1, "message": "2063", "line": 2716, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 2716, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2064", "line": 2750, "column": 6, "nodeType": "1878", "messageId": "1879", "endLine": 2750, "endColumn": 18}, {"ruleId": "1980", "severity": 1, "message": "2065", "line": 3134, "column": 4, "nodeType": "1982", "endLine": 3134, "endColumn": 18, "suggestions": "2066"}, {"ruleId": "1876", "severity": 1, "message": "2067", "line": 3478, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3478, "endColumn": 33}, {"ruleId": "1980", "severity": 1, "message": "2068", "line": 3552, "column": 16, "nodeType": "2037", "endLine": 3552, "endColumn": 37}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3553, "column": 56, "nodeType": "2032", "messageId": "2033", "endLine": 3553, "endColumn": 58}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3557, "column": 49, "nodeType": "2032", "messageId": "2033", "endLine": 3557, "endColumn": 51}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3561, "column": 50, "nodeType": "2032", "messageId": "2033", "endLine": 3561, "endColumn": 52}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3567, "column": 51, "nodeType": "2032", "messageId": "2033", "endLine": 3567, "endColumn": 53}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3574, "column": 51, "nodeType": "2032", "messageId": "2033", "endLine": 3574, "endColumn": 53}, {"ruleId": "1876", "severity": 1, "message": "2069", "line": 3797, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3797, "endColumn": 23}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 3805, "column": 30, "nodeType": "2032", "messageId": "2033", "endLine": 3805, "endColumn": 32}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3818, "column": 39, "nodeType": "2032", "messageId": "2033", "endLine": 3818, "endColumn": 41}, {"ruleId": "1980", "severity": 1, "message": "2070", "line": 3832, "column": 5, "nodeType": "1982", "endLine": 3832, "endColumn": 33, "suggestions": "2071"}, {"ruleId": "1876", "severity": 1, "message": "2072", "line": 3836, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 3836, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2073", "line": 3836, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 3836, "endColumn": 52}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 3933, "column": 55, "nodeType": "2032", "messageId": "2033", "endLine": 3933, "endColumn": 57}, {"ruleId": "1876", "severity": 1, "message": "2074", "line": 3952, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3952, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2075", "line": 3954, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 3954, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2076", "line": 3958, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3958, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2077", "line": 3977, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 3977, "endColumn": 26}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 4001, "column": 66, "nodeType": "2032", "messageId": "2033", "endLine": 4001, "endColumn": 68}, {"ruleId": "1980", "severity": 1, "message": "2078", "line": 4008, "column": 5, "nodeType": "1982", "endLine": 4015, "endColumn": 3, "suggestions": "2079"}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 4249, "column": 17, "nodeType": "2032", "messageId": "2033", "endLine": 4249, "endColumn": 19}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 4503, "column": 21, "nodeType": "2032", "messageId": "2033", "endLine": 4503, "endColumn": 23}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 4511, "column": 21, "nodeType": "2032", "messageId": "2033", "endLine": 4511, "endColumn": 23}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 4524, "column": 15, "nodeType": "2032", "messageId": "2033", "endLine": 4524, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2080", "line": 4821, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 4821, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2081", "line": 4832, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 4832, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2082", "line": 4833, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 4833, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2083", "line": 4839, "column": 5, "nodeType": "1982", "endLine": 4839, "endColumn": 62, "suggestions": "2084"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 4839, "column": 6, "nodeType": "2085", "endLine": 4839, "endColumn": 48}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 4862, "column": 25, "nodeType": "2032", "messageId": "2033", "endLine": 4862, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2086", "line": 4866, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4866, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2087", "line": 4889, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 4889, "endColumn": 23}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 4964, "column": 25, "nodeType": "2032", "messageId": "2033", "endLine": 4964, "endColumn": 27}, {"ruleId": "1980", "severity": 1, "message": "2088", "line": 4997, "column": 5, "nodeType": "1982", "endLine": 4997, "endColumn": 22, "suggestions": "2089"}, {"ruleId": "1876", "severity": 1, "message": "2090", "line": 4999, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4999, "endColumn": 18}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 5001, "column": 40, "nodeType": "2032", "messageId": "2033", "endLine": 5001, "endColumn": 42}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 5066, "column": 69, "nodeType": "2032", "messageId": "2033", "endLine": 5066, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2091", "line": 5116, "column": 12, "nodeType": "1878", "messageId": "1879", "endLine": 5116, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2092", "line": 5117, "column": 12, "nodeType": "1878", "messageId": "1879", "endLine": 5117, "endColumn": 22}, {"ruleId": "1980", "severity": 1, "message": "2093", "line": 5147, "column": 5, "nodeType": "1982", "endLine": 5147, "endColumn": 38, "suggestions": "2094"}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 5150, "column": 40, "nodeType": "2032", "messageId": "2033", "endLine": 5150, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2091", "line": 5156, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5156, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2092", "line": 5157, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5157, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2095", "line": 5163, "column": 5, "nodeType": "1982", "endLine": 5163, "endColumn": 106, "suggestions": "2096"}, {"ruleId": "1980", "severity": 1, "message": "2097", "line": 5276, "column": 5, "nodeType": "1982", "endLine": 5276, "endColumn": 17, "suggestions": "2098"}, {"ruleId": "1980", "severity": 1, "message": "2099", "line": 5292, "column": 5, "nodeType": "1982", "endLine": 5292, "endColumn": 78, "suggestions": "2100"}, {"ruleId": "1876", "severity": 1, "message": "2101", "line": 5295, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 5295, "endColumn": 29}, {"ruleId": "2102", "severity": 1, "message": "2103", "line": 5904, "column": 80, "nodeType": "2104", "messageId": "2105", "endLine": 5904, "endColumn": 81, "suggestions": "2106"}, {"ruleId": "1876", "severity": 1, "message": "2107", "line": 6109, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 6109, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2108", "line": 2, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2109", "line": 7, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2110", "line": 7, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2111", "line": 98, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 98, "endColumn": 25}, {"ruleId": "1980", "severity": 1, "message": "2112", "line": 103, "column": 6, "nodeType": "1982", "endLine": 103, "endColumn": 8, "suggestions": "2113"}, {"ruleId": "1876", "severity": 1, "message": "2114", "line": 148, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 148, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2115", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2116", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 3, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2118", "line": 8, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2119", "line": 9, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2120", "line": 13, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 46}, {"ruleId": "2121", "severity": 1, "message": "2122", "line": 2422, "column": 5, "nodeType": "2123", "messageId": "2033", "endLine": 2422, "endColumn": 17}, {"ruleId": "2121", "severity": 1, "message": "2124", "line": 2423, "column": 5, "nodeType": "2123", "messageId": "2033", "endLine": 2423, "endColumn": 20}, {"ruleId": "2121", "severity": 1, "message": "2125", "line": 2754, "column": 5, "nodeType": "2123", "messageId": "2033", "endLine": 2754, "endColumn": 24}, {"ruleId": "2121", "severity": 1, "message": "2126", "line": 2931, "column": 5, "nodeType": "2123", "messageId": "2033", "endLine": 2931, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2127", "line": 3635, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 3635, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2127", "line": 3834, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 3834, "endColumn": 28}, {"ruleId": "2121", "severity": 1, "message": "2128", "line": 5321, "column": 5, "nodeType": "2123", "messageId": "2033", "endLine": 5321, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2129", "line": 5393, "column": 14, "nodeType": "1878", "messageId": "1879", "endLine": 5393, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2130", "line": 6484, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 6484, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2130", "line": 6510, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 6510, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2130", "line": 6516, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 6516, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2130", "line": 6531, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 6531, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2129", "line": 7308, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 7308, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2129", "line": 7552, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 7552, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2129", "line": 7723, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 7723, "endColumn": 16}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 8388, "column": 66, "nodeType": "2032", "messageId": "2033", "endLine": 8388, "endColumn": 68}, {"ruleId": "1876", "severity": 1, "message": "2131", "line": 70, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 70, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2132", "line": 1, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2133", "line": 2, "column": 44, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 56}, {"ruleId": "1876", "severity": 1, "message": "2134", "line": 18, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2135", "line": 19, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 20, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2137", "line": 21, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 21, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2138", "line": 24, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2139", "line": 25, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2140", "line": 26, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2141", "line": 31, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2142", "line": 38, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 55}, {"ruleId": "1876", "severity": 1, "message": "2143", "line": 38, "column": 63, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 70}, {"ruleId": "1876", "severity": 1, "message": "2144", "line": 46, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2145", "line": 48, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2146", "line": 92, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2147", "line": 93, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 93, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2148", "line": 99, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 99, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2149", "line": 100, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2150", "line": 104, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2151", "line": 108, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 108, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2152", "line": 112, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 112, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2153", "line": 113, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 113, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2154", "line": 114, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 114, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2155", "line": 115, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 115, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2156", "line": 116, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 116, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 119, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 119, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2158", "line": 120, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 120, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2159", "line": 162, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 162, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2160", "line": 172, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 172, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2161", "line": 180, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 180, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2162", "line": 181, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 181, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2163", "line": 182, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 182, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2164", "line": 183, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 183, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2165", "line": 184, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 184, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2166", "line": 205, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 205, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2167", "line": 209, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 209, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2168", "line": 455, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 455, "endColumn": 26}, {"ruleId": "1980", "severity": 1, "message": "2169", "line": 547, "column": 5, "nodeType": "1982", "endLine": 547, "endColumn": 60, "suggestions": "2170"}, {"ruleId": "1876", "severity": 1, "message": "2171", "line": 561, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 561, "endColumn": 22}, {"ruleId": "1980", "severity": 1, "message": "2172", "line": 581, "column": 5, "nodeType": "1982", "endLine": 581, "endColumn": 60, "suggestions": "2173"}, {"ruleId": "1980", "severity": 1, "message": "2174", "line": 598, "column": 4, "nodeType": "1982", "endLine": 598, "endColumn": 6, "suggestions": "2175"}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2177", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "1917", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "1918", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1915", "line": 5, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "1916", "line": 5, "column": 46, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 55}, {"ruleId": "1876", "severity": 1, "message": "2179", "line": 6, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2180", "line": 6, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "1927", "line": 11, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2181", "line": 17, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2182", "line": 21, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 21, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2183", "line": 24, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2184", "line": 25, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2185", "line": 26, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1960", "line": 35, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 35, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1961", "line": 35, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 35, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1892", "line": 6, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2186", "line": 9, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2187", "line": 12, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2188", "line": 25, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2189", "line": 51, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2190", "line": 52, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1999", "line": 53, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2191", "line": 54, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 54, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2192", "line": 55, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 55, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2016", "line": 56, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2193", "line": 57, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 57, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2194", "line": 58, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2017", "line": 59, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2195", "line": 60, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2196", "line": 61, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2197", "line": 62, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 62, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2198", "line": 63, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 63, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2199", "line": 64, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 64, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2153", "line": 65, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 65, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2156", "line": 66, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 66, "endColumn": 23}, {"ruleId": "1980", "severity": 1, "message": "2200", "line": 78, "column": 5, "nodeType": "1982", "endLine": 78, "endColumn": 7, "suggestions": "2201"}, {"ruleId": "1980", "severity": 1, "message": "2202", "line": 96, "column": 5, "nodeType": "1982", "endLine": 96, "endColumn": 28, "suggestions": "2203"}, {"ruleId": "1980", "severity": 1, "message": "2204", "line": 107, "column": 5, "nodeType": "1982", "endLine": 107, "endColumn": 48, "suggestions": "2205"}, {"ruleId": "1876", "severity": 1, "message": "2206", "line": 186, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 186, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2207", "line": 247, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 247, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2208", "line": 319, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 319, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2209", "line": 706, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 706, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2210", "line": 711, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 711, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2211", "line": 1, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2212", "line": 3, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2213", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1883", "line": 2, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2214", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2215", "line": 8, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2216", "line": 8, "column": 44, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 59}, {"ruleId": "1876", "severity": 1, "message": "2217", "line": 10, "column": 34, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 57}, {"ruleId": "1876", "severity": 1, "message": "2218", "line": 10, "column": 59, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 79}, {"ruleId": "1876", "severity": 1, "message": "2219", "line": 59, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2220", "line": 124, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 124, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1883", "line": 1, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2221", "line": 8, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2215", "line": 9, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2222", "line": 80, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 80, "endColumn": 58}, {"ruleId": "1980", "severity": 1, "message": "2223", "line": 86, "column": 8, "nodeType": "2044", "endLine": 90, "endColumn": 12}, {"ruleId": "1980", "severity": 1, "message": "2224", "line": 86, "column": 8, "nodeType": "2044", "endLine": 90, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2225", "line": 92, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2226", "line": 92, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 42}, {"ruleId": "2227", "severity": 1, "message": "2228", "line": 113, "column": 113, "nodeType": "2229", "messageId": "2230", "endLine": 113, "endColumn": 397}, {"ruleId": "1980", "severity": 1, "message": "2231", "line": 154, "column": 5, "nodeType": "1982", "endLine": 154, "endColumn": 38, "suggestions": "2232"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 154, "column": 6, "nodeType": "2032", "endLine": 154, "endColumn": 37}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 154, "column": 33, "nodeType": "2032", "messageId": "2033", "endLine": 154, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2219", "line": 156, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 156, "endColumn": 24}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 174, "column": 56, "nodeType": "2032", "messageId": "2033", "endLine": 174, "endColumn": 58}, {"ruleId": "1876", "severity": 1, "message": "2233", "line": 181, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 181, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2234", "line": 182, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 182, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2235", "line": 305, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 305, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2219", "line": 771, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 771, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2236", "line": 806, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 806, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2237", "line": 806, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 806, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2238", "line": 807, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 807, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2239", "line": 807, "column": 22, "nodeType": "1878", "messageId": "1879", "endLine": 807, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "2058", "line": 808, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 808, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2059", "line": 808, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 808, "endColumn": 48}, {"ruleId": "1876", "severity": 1, "message": "2240", "line": 809, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 809, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2241", "line": 809, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 809, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "1974", "line": 810, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 810, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2242", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2243", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2244", "line": 31, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2245", "line": 32, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 32, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2177", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "1887", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2181", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 9, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 11, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 12, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2247", "line": 14, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2140", "line": 16, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 18, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 19, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 20, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 21, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 21, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2252", "line": 22, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 23, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2254", "line": 24, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2255", "line": 25, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "1912", "line": 3, "column": 65, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 69}, {"ruleId": "1876", "severity": 1, "message": "2256", "line": 6, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2257", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2258", "line": 20, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2237", "line": 84, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 84, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2259", "line": 85, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2260", "line": 85, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2261", "line": 86, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 86, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2262", "line": 86, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 86, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2263", "line": 90, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 90, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2264", "line": 90, "column": 45, "nodeType": "1878", "messageId": "1879", "endLine": 90, "endColumn": 62}, {"ruleId": "1876", "severity": 1, "message": "2265", "line": 93, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 93, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2266", "line": 94, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 94, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1967", "line": 95, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 95, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "1968", "line": 96, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 96, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1969", "line": 97, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 97, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "1970", "line": 98, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 98, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "1971", "line": 99, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 99, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1972", "line": 100, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2267", "line": 101, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 101, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 102, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 102, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2269", "line": 103, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 103, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 104, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2270", "line": 105, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 105, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2271", "line": 107, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2272", "line": 108, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 108, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2273", "line": 115, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 115, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2274", "line": 116, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 116, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2275", "line": 118, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 118, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2276", "line": 119, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 119, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2277", "line": 120, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 120, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2278", "line": 121, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 121, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2279", "line": 122, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 122, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2280", "line": 123, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 123, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2281", "line": 132, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 132, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2282", "line": 137, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 137, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2147", "line": 139, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 139, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2154", "line": 140, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 140, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2283", "line": 141, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 141, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2284", "line": 144, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 144, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2152", "line": 145, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 145, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2285", "line": 146, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 146, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2286", "line": 170, "column": 5, "nodeType": "1982", "endLine": 170, "endColumn": 45, "suggestions": "2287"}, {"ruleId": "1876", "severity": 1, "message": "2288", "line": 221, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 221, "endColumn": 29}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 241, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 241, "endColumn": 26}, {"ruleId": "1980", "severity": 1, "message": "2289", "line": 315, "column": 7, "nodeType": "1982", "endLine": 315, "endColumn": 42, "suggestions": "2290"}, {"ruleId": "1876", "severity": 1, "message": "2291", "line": 339, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 339, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2292", "line": 340, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 340, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2293", "line": 488, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 488, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2294", "line": 491, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 491, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2295", "line": 500, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 500, "endColumn": 31}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 811, "column": 26, "nodeType": "2032", "messageId": "2033", "endLine": 811, "endColumn": 28}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 847, "column": 26, "nodeType": "2032", "messageId": "2033", "endLine": 847, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2296", "line": 1031, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1031, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2297", "line": 1035, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1035, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2298", "line": 1039, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1039, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2299", "line": 1043, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1043, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2300", "line": 1047, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1047, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2301", "line": 1051, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1051, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2302", "line": 1055, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1055, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2303", "line": 1059, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1059, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2304", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2305", "line": 13, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2306", "line": 15, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2307", "line": 79, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 79, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2308", "line": 81, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 81, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2309", "line": 82, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2310", "line": 83, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 83, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2311", "line": 84, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 84, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1994", "line": 88, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 88, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2312", "line": 89, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 89, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2313", "line": 91, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 91, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "1996", "line": 92, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1997", "line": 93, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 93, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 94, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 94, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 104, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 19}, {"ruleId": "1980", "severity": 1, "message": "2314", "line": 209, "column": 7, "nodeType": "1982", "endLine": 209, "endColumn": 9, "suggestions": "2315"}, {"ruleId": "1980", "severity": 1, "message": "2316", "line": 244, "column": 7, "nodeType": "1982", "endLine": 244, "endColumn": 29, "suggestions": "2317"}, {"ruleId": "1980", "severity": 1, "message": "2318", "line": 249, "column": 7, "nodeType": "1982", "endLine": 249, "endColumn": 18, "suggestions": "2319"}, {"ruleId": "1980", "severity": 1, "message": "2320", "line": 292, "column": 7, "nodeType": "1982", "endLine": 292, "endColumn": 72, "suggestions": "2321"}, {"ruleId": "1876", "severity": 1, "message": "2270", "line": 331, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 331, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2322", "line": 334, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 334, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2323", "line": 463, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 463, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2324", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2325", "line": 6, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2326", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2327", "line": 7, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2328", "line": 8, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2329", "line": 9, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1996", "line": 13, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1997", "line": 14, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 15, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2330", "line": 17, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2331", "line": 18, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2332", "line": 18, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2333", "line": 19, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2334", "line": 96, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 96, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2335", "line": 97, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 97, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2336", "line": 100, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2337", "line": 101, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 101, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2338", "line": 109, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 109, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2339", "line": 129, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 129, "endColumn": 16}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 176, "column": 45, "nodeType": "2032", "messageId": "2033", "endLine": 176, "endColumn": 47}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 176, "column": 104, "nodeType": "2032", "messageId": "2033", "endLine": 176, "endColumn": 106}, {"ruleId": "1876", "severity": 1, "message": "2141", "line": 2, "column": 60, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 73}, {"ruleId": "1876", "severity": 1, "message": "2324", "line": 3, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2340", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2341", "line": 9, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2342", "line": 133, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 133, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2343", "line": 134, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 134, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2344", "line": 135, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 135, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2345", "line": 135, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 135, "endColumn": 52}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 137, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 137, "endColumn": 19}, {"ruleId": "1980", "severity": 1, "message": "2346", "line": 163, "column": 8, "nodeType": "1982", "endLine": 163, "endColumn": 10, "suggestions": "2347"}, {"ruleId": "1876", "severity": 1, "message": "2348", "line": 299, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 299, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2349", "line": 342, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 342, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2350", "line": 343, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 343, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2351", "line": 344, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 344, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2352", "line": 346, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 346, "endColumn": 20}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 465, "column": 22, "nodeType": "2032", "messageId": "2033", "endLine": 465, "endColumn": 24}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 465, "column": 53, "nodeType": "2032", "messageId": "2033", "endLine": 465, "endColumn": 55}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 465, "column": 89, "nodeType": "2032", "messageId": "2033", "endLine": 465, "endColumn": 91}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 465, "column": 125, "nodeType": "2032", "messageId": "2033", "endLine": 465, "endColumn": 127}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 467, "column": 29, "nodeType": "2032", "messageId": "2033", "endLine": 467, "endColumn": 31}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 467, "column": 56, "nodeType": "2032", "messageId": "2033", "endLine": 467, "endColumn": 58}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 467, "column": 88, "nodeType": "2032", "messageId": "2033", "endLine": 467, "endColumn": 90}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 467, "column": 120, "nodeType": "2032", "messageId": "2033", "endLine": 467, "endColumn": 122}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 469, "column": 29, "nodeType": "2032", "messageId": "2033", "endLine": 469, "endColumn": 31}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 469, "column": 64, "nodeType": "2032", "messageId": "2033", "endLine": 469, "endColumn": 66}, {"ruleId": "1876", "severity": 1, "message": "2353", "line": 111, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 111, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2280", "line": 152, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 152, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2008", "line": 153, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 153, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2354", "line": 159, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 159, "endColumn": 23}, {"ruleId": "2355", "severity": 1, "message": "2356", "line": 225, "column": 25, "nodeType": "1878", "messageId": "2357", "endLine": 225, "endColumn": 34, "suggestions": "2358"}, {"ruleId": "1980", "severity": 1, "message": "2359", "line": 231, "column": 5, "nodeType": "1982", "endLine": 231, "endColumn": 12, "suggestions": "2360"}, {"ruleId": "1980", "severity": 1, "message": "2361", "line": 237, "column": 5, "nodeType": "1982", "endLine": 237, "endColumn": 21, "suggestions": "2362"}, {"ruleId": "1980", "severity": 1, "message": "2363", "line": 472, "column": 5, "nodeType": "1982", "endLine": 472, "endColumn": 70, "suggestions": "2364"}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 547, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 547, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 548, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 548, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 549, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 549, "endColumn": 26}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 550, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 550, "endColumn": 26}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 554, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 554, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 555, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 555, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 556, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 556, "endColumn": 26}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 557, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 557, "endColumn": 26}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 561, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 561, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 562, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 562, "endColumn": 26}, {"ruleId": "1980", "severity": 1, "message": "2365", "line": 582, "column": 5, "nodeType": "1982", "endLine": 582, "endColumn": 64, "suggestions": "2366"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 582, "column": 6, "nodeType": "2085", "endLine": 582, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2367", "line": 591, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 591, "endColumn": 21}, {"ruleId": "1980", "severity": 1, "message": "2368", "line": 605, "column": 5, "nodeType": "1982", "endLine": 605, "endColumn": 47, "suggestions": "2369"}, {"ruleId": "1876", "severity": 1, "message": "2367", "line": 614, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 614, "endColumn": 21}, {"ruleId": "1980", "severity": 1, "message": "2368", "line": 627, "column": 5, "nodeType": "1982", "endLine": 627, "endColumn": 47, "suggestions": "2370"}, {"ruleId": "1980", "severity": 1, "message": "2371", "line": 1021, "column": 17, "nodeType": "1878", "endLine": 1021, "endColumn": 32}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 1227, "column": 43, "nodeType": "2032", "messageId": "2033", "endLine": 1227, "endColumn": 45}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 1232, "column": 78, "nodeType": "2032", "messageId": "2033", "endLine": 1232, "endColumn": 80}, {"ruleId": "1876", "severity": 1, "message": "2372", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2373", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2374", "line": 2, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 3, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2375", "line": 11, "column": 62, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 67}, {"ruleId": "1876", "severity": 1, "message": "2198", "line": 25, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2156", "line": 28, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2376", "line": 31, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2377", "line": 144, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 144, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2378", "line": 145, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 145, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2379", "line": 1, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 5, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 5}, {"ruleId": "1876", "severity": 1, "message": "2380", "line": 6, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2381", "line": 10, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2382", "line": 12, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 13, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2383", "line": 17, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2221", "line": 19, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2384", "line": 33, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 33, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2238", "line": 33, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 33, "endColumn": 44}, {"ruleId": "2385", "severity": 1, "message": "2386", "line": 95, "column": 2, "nodeType": "2387", "messageId": "2388", "endLine": 111, "endColumn": 4}, {"ruleId": "1876", "severity": 1, "message": "2389", "line": 132, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 132, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2390", "line": 135, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 135, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 136, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 136, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 137, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 137, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2391", "line": 139, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 139, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2265", "line": 140, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 140, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2392", "line": 141, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 141, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2393", "line": 144, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 144, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2394", "line": 145, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 145, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2395", "line": 146, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 146, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2396", "line": 147, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 147, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2397", "line": 148, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 148, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2398", "line": 149, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 149, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2275", "line": 150, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 150, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2274", "line": 151, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 151, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2273", "line": 152, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 152, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2399", "line": 155, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 155, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2400", "line": 158, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 158, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2401", "line": 159, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 159, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2402", "line": 160, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 160, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2191", "line": 161, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 161, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2151", "line": 162, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 162, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2403", "line": 165, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 165, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2404", "line": 166, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 166, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2354", "line": 168, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 168, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2405", "line": 173, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 173, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2008", "line": 174, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 174, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2406", "line": 185, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 185, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2407", "line": 185, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 185, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2408", "line": 187, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 187, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2409", "line": 187, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 187, "endColumn": 44}, {"ruleId": "2410", "severity": 1, "message": "2411", "line": 349, "column": 5, "nodeType": "2412", "messageId": "2413", "endLine": 349, "endColumn": 52}, {"ruleId": "1876", "severity": 1, "message": "2414", "line": 504, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 504, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2415", "line": 511, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 511, "endColumn": 21}, {"ruleId": "1980", "severity": 1, "message": "2416", "line": 668, "column": 5, "nodeType": "1982", "endLine": 668, "endColumn": 100, "suggestions": "2417"}, {"ruleId": "1980", "severity": 1, "message": "2418", "line": 775, "column": 5, "nodeType": "1982", "endLine": 775, "endColumn": 22, "suggestions": "2419"}, {"ruleId": "1876", "severity": 1, "message": "2420", "line": 869, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 869, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2421", "line": 876, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 876, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2372", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2422", "line": 1, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "2423", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2189", "line": 14, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2195", "line": 15, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2190", "line": 16, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2156", "line": 17, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2199", "line": 20, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2424", "line": 26, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2422", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2425", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 2, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 2, "column": 39, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2381", "line": 2, "column": 44, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 58}, {"ruleId": "1876", "severity": 1, "message": "2141", "line": 2, "column": 60, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 73}, {"ruleId": "1876", "severity": 1, "message": "2252", "line": 2, "column": 74, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 84}, {"ruleId": "1876", "severity": 1, "message": "2426", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2427", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2189", "line": 98, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 98, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2195", "line": 99, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 99, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2190", "line": 100, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "1999", "line": 101, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 101, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2191", "line": 102, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 102, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2192", "line": 103, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 103, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2016", "line": 104, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2017", "line": 105, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 105, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2239", "line": 110, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 110, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 113, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 113, "endColumn": 29}, {"ruleId": "1980", "severity": 1, "message": "2428", "line": 172, "column": 12, "nodeType": "1982", "endLine": 172, "endColumn": 35, "suggestions": "2429"}, {"ruleId": "1876", "severity": 1, "message": "1883", "line": 1, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2110", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2213", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "1925", "line": 7, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2430", "line": 43, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 34}, {"ruleId": "1980", "severity": 1, "message": "2431", "line": 63, "column": 21, "nodeType": "2432", "endLine": 63, "endColumn": 111}, {"ruleId": "1876", "severity": 1, "message": "2433", "line": 2, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "2434", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2109", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2435", "line": 11, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2436", "line": 1, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2437", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "1914", "line": 1, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2437", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2438", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 40}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2439", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2439", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2252", "line": 2, "column": 50, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 60}, {"ruleId": "1876", "severity": 1, "message": "2426", "line": 29, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2180", "line": 34, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2405", "line": 64, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 64, "endColumn": 43}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 72, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 72, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2441", "line": 74, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 74, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2442", "line": 97, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 97, "endColumn": 22}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 106, "column": 34, "nodeType": "2032", "messageId": "2033", "endLine": 106, "endColumn": 36}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 154, "column": 44, "nodeType": "2032", "messageId": "2033", "endLine": 154, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "2443", "line": 173, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 173, "endColumn": 21}, {"ruleId": "1980", "severity": 1, "message": "2444", "line": 301, "column": 5, "nodeType": "1982", "endLine": 301, "endColumn": 50, "suggestions": "2445"}, {"ruleId": "1980", "severity": 1, "message": "2444", "line": 317, "column": 5, "nodeType": "1982", "endLine": 317, "endColumn": 18, "suggestions": "2446"}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 477, "column": 48, "nodeType": "2032", "messageId": "2033", "endLine": 477, "endColumn": 50}, {"ruleId": "1876", "severity": 1, "message": "2372", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2447", "line": 61, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2448", "line": 63, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 63, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2450", "line": 6, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 22}, {"ruleId": "1980", "severity": 1, "message": "2451", "line": 344, "column": 8, "nodeType": "1982", "endLine": 344, "endColumn": 64, "suggestions": "2452"}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 44, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 53}, {"ruleId": "1876", "severity": 1, "message": "2453", "line": 4, "column": 46, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 65}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 4, "column": 67, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 75}, {"ruleId": "1876", "severity": 1, "message": "2426", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2454", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2189", "line": 30, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 30, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2455", "line": 31, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 34, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2456", "line": 44, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2457", "line": 45, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2458", "line": 46, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2459", "line": 47, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2460", "line": 51, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2461", "line": 51, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2462", "line": 52, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2463", "line": 53, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2464", "line": 56, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2465", "line": 57, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 57, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2466", "line": 57, "column": 20, "nodeType": "1878", "messageId": "1879", "endLine": 57, "endColumn": 32}, {"ruleId": "1980", "severity": 1, "message": "2467", "line": 65, "column": 5, "nodeType": "1982", "endLine": 65, "endColumn": 7, "suggestions": "2468"}, {"ruleId": "1876", "severity": 1, "message": "2469", "line": 93, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 93, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2470", "line": 97, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 97, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2471", "line": 124, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 124, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2472", "line": 132, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 132, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2473", "line": 136, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 136, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2474", "line": 150, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 150, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2475", "line": 153, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 153, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "1883", "line": 5, "column": 28, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2476", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2225", "line": 85, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2477", "line": 98, "column": 6, "nodeType": "1982", "endLine": 98, "endColumn": 8, "suggestions": "2478"}, {"ruleId": "1980", "severity": 1, "message": "2479", "line": 121, "column": 6, "nodeType": "1982", "endLine": 121, "endColumn": 32, "suggestions": "2480"}, {"ruleId": "1980", "severity": 1, "message": "2231", "line": 125, "column": 6, "nodeType": "1982", "endLine": 125, "endColumn": 40, "suggestions": "2481"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 125, "column": 7, "nodeType": "2032", "endLine": 125, "endColumn": 39}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 125, "column": 35, "nodeType": "2032", "messageId": "2033", "endLine": 125, "endColumn": 37}, {"ruleId": "1980", "severity": 1, "message": "2482", "line": 148, "column": 6, "nodeType": "1982", "endLine": 148, "endColumn": 33, "suggestions": "2483"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 148, "column": 7, "nodeType": "2037", "endLine": 148, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2484", "line": 156, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 156, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2485", "line": 2, "column": 14, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2486", "line": 16, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2487", "line": 19, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2488", "line": 22, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 20}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 43, "column": 100, "nodeType": "2032", "messageId": "2033", "endLine": 43, "endColumn": 102}, {"ruleId": "1876", "severity": 1, "message": "2489", "line": 4, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2490", "line": 4, "column": 32, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 45}, {"ruleId": "1876", "severity": 1, "message": "2491", "line": 10, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2492", "line": 65, "column": 12, "nodeType": "1878", "messageId": "1879", "endLine": 65, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2493", "line": 65, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 65, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2494", "line": 78, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 78, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2495", "line": 78, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 78, "endColumn": 39}, {"ruleId": "1980", "severity": 1, "message": "2496", "line": 120, "column": 6, "nodeType": "1982", "endLine": 120, "endColumn": 8, "suggestions": "2497"}, {"ruleId": "1876", "severity": 1, "message": "2498", "line": 157, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 157, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2499", "line": 280, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 280, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2500", "line": 296, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 296, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2501", "line": 461, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 461, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2502", "line": 462, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 462, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2503", "line": 467, "column": 3, "nodeType": "1982", "endLine": 467, "endColumn": 5, "suggestions": "2504"}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 2, "column": 80, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 2, "column": 105, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 111}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 2, "column": 113, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 121}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2508", "line": 2, "column": 168, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 180}, {"ruleId": "1876", "severity": 1, "message": "2509", "line": 2, "column": 182, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 199}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 4, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 4, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 4, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 13, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 14, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 15, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 16, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 17, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2518", "line": 24, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2519", "line": 25, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2520", "line": 26, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2521", "line": 27, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2522", "line": 28, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2523", "line": 29, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2524", "line": 30, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 30, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2525", "line": 40, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2526", "line": 41, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 41, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2527", "line": 43, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2528", "line": 45, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2233", "line": 47, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2529", "line": 94, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 94, "endColumn": 19}, {"ruleId": "1980", "severity": 1, "message": "2530", "line": 125, "column": 5, "nodeType": "1982", "endLine": 125, "endColumn": 7, "suggestions": "2531"}, {"ruleId": "1876", "severity": 1, "message": "2532", "line": 145, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 145, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2533", "line": 162, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 162, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2534", "line": 165, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 165, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2535", "line": 170, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 170, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2536", "line": 211, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 211, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2537", "line": 214, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 214, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2538", "line": 227, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 227, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2539", "line": 228, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 228, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2540", "line": 228, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 228, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2541", "line": 229, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 229, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2542", "line": 229, "column": 20, "nodeType": "1878", "messageId": "1879", "endLine": 229, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "1962", "line": 245, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 245, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1963", "line": 245, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 245, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2543", "line": 247, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 247, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2544", "line": 261, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 261, "endColumn": 36}, {"ruleId": "1980", "severity": 1, "message": "2545", "line": 281, "column": 4, "nodeType": "1982", "endLine": 281, "endColumn": 6, "suggestions": "2546"}, {"ruleId": "1876", "severity": 1, "message": "2544", "line": 334, "column": 12, "nodeType": "1878", "messageId": "1879", "endLine": 334, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2547", "line": 347, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 347, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2548", "line": 347, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 347, "endColumn": 45}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 2, "column": 80, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 2, "column": 105, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 111}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 2, "column": 113, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 121}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 168, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 175}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 4, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 4, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 4, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 8, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 9, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 10, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 11, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 12, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2549", "line": 13, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2550", "line": 14, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2551", "line": 15, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2552", "line": 22, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2553", "line": 31, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2217", "line": 32, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 32, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2525", "line": 35, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 35, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2526", "line": 36, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 36, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2527", "line": 38, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2554", "line": 39, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2555", "line": 40, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2556", "line": 42, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 42, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2557", "line": 43, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2558", "line": 44, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2559", "line": 45, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2560", "line": 46, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2561", "line": 47, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2562", "line": 48, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2563", "line": 49, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2564", "line": 50, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2565", "line": 51, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 58, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2487", "line": 60, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2529", "line": 75, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 75, "endColumn": 19}, {"ruleId": "1980", "severity": 1, "message": "2482", "line": 87, "column": 5, "nodeType": "1982", "endLine": 87, "endColumn": 45, "suggestions": "2566"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 87, "column": 6, "nodeType": "2085", "endLine": 87, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2567", "line": 106, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 106, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2568", "line": 106, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 106, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2569", "line": 107, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2570", "line": 107, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2571", "line": 108, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 108, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2150", "line": 109, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 109, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2572", "line": 109, "column": 18, "nodeType": "1878", "messageId": "1879", "endLine": 109, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2573", "line": 110, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 110, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2533", "line": 115, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 115, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2535", "line": 119, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 119, "endColumn": 25}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 134, "column": 11, "nodeType": "2032", "messageId": "2033", "endLine": 134, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2574", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2575", "line": 5, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2576", "line": 10, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2508", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "2509", "line": 2, "column": 41, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 58}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 72, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 88}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 90, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 96}, {"ruleId": "1876", "severity": 1, "message": "2577", "line": 9, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 6, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 9, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 10, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 11, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 12, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "2578", "line": 19, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2285", "line": 35, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 35, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2579", "line": 37, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 37, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2384", "line": 38, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2238", "line": 39, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2580", "line": 40, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2152", "line": 42, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 42, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 48, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2581", "line": 55, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 55, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2582", "line": 56, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2583", "line": 57, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 57, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2584", "line": 86, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 86, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2585", "line": 90, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 90, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2586", "line": 95, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 95, "endColumn": 33}, {"ruleId": "1980", "severity": 1, "message": "2587", "line": 195, "column": 5, "nodeType": "1982", "endLine": 195, "endColumn": 30, "suggestions": "2588"}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 3, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 5}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 4, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 9, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2574", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 32}, {"ruleId": "1876", "severity": 1, "message": "2575", "line": 5, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2589", "line": 9, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2590", "line": 9, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2576", "line": 9, "column": 32, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 41}, {"ruleId": "1876", "severity": 1, "message": "2591", "line": 9, "column": 43, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 51}, {"ruleId": "1876", "severity": 1, "message": "2592", "line": 9, "column": 53, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 63}, {"ruleId": "1876", "severity": 1, "message": "2593", "line": 9, "column": 65, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 77}, {"ruleId": "1876", "severity": 1, "message": "2594", "line": 9, "column": 79, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 90}, {"ruleId": "1876", "severity": 1, "message": "2595", "line": 9, "column": 92, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 102}, {"ruleId": "1876", "severity": 1, "message": "2596", "line": 9, "column": 104, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 116}, {"ruleId": "1876", "severity": 1, "message": "2597", "line": 9, "column": 118, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 129}, {"ruleId": "1876", "severity": 1, "message": "2598", "line": 9, "column": 131, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2599", "line": 15, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 16, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2600", "line": 17, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2392", "line": 18, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2601", "line": 19, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 20, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2391", "line": 23, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2602", "line": 24, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2603", "line": 25, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2604", "line": 26, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2605", "line": 27, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2606", "line": 28, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2607", "line": 29, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2608", "line": 30, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 30, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 34, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2529", "line": 62, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 62, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2585", "line": 82, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2609", "line": 83, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 83, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 3, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 3, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 3, "column": 80, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 3, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 3, "column": 105, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 111}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 3, "column": 113, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 121}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 3, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 3, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 3, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 5, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 5, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 5, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 6, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 8, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 9, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 10, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 11, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2525", "line": 19, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2527", "line": 22, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2610", "line": 24, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2611", "line": 25, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2612", "line": 26, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2613", "line": 27, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 31, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2529", "line": 36, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 36, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2614", "line": 104, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2615", "line": 105, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 105, "endColumn": 40}, {"ruleId": "1876", "severity": 1, "message": "2532", "line": 120, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 120, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2533", "line": 154, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 154, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2616", "line": 157, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 157, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 3, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 3, "column": 56, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 63}, {"ruleId": "1876", "severity": 1, "message": "2153", "line": 13, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2265", "line": 14, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2266", "line": 15, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1967", "line": 16, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "1969", "line": 18, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "1970", "line": 19, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "1971", "line": 20, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1972", "line": 21, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 21, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2267", "line": 22, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 23, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2269", "line": 24, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 25, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2617", "line": 37, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 37, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2307", "line": 39, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2618", "line": 41, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 41, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2619", "line": 45, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2620", "line": 49, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2621", "line": 49, "column": 25, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2622", "line": 50, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2623", "line": 50, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2624", "line": 51, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2625", "line": 51, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2626", "line": 52, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2627", "line": 52, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 52}, {"ruleId": "1876", "severity": 1, "message": "2628", "line": 53, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2629", "line": 53, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "2630", "line": 3, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2631", "line": 4, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2632", "line": 5, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2633", "line": 6, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2425", "line": 9, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "2134", "line": 17, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2135", "line": 18, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 19, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2137", "line": 20, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 7}, {"ruleId": "1876", "severity": 1, "message": "2138", "line": 23, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2139", "line": 24, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2140", "line": 25, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 25, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 26, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "2325", "line": 43, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2634", "line": 44, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2635", "line": 50, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2636", "line": 51, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2637", "line": 53, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2638", "line": 54, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 54, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2639", "line": 55, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 55, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2640", "line": 56, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2153", "line": 58, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2189", "line": 59, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2455", "line": 60, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2147", "line": 62, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 62, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2148", "line": 68, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 68, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2149", "line": 69, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 69, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2150", "line": 75, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 75, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2641", "line": 77, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 77, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1996", "line": 80, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 80, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1997", "line": 81, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 81, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 82, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2642", "line": 83, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 83, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2643", "line": 84, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 84, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2644", "line": 85, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2163", "line": 87, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 87, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2161", "line": 89, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 89, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2165", "line": 91, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 91, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "2645", "line": 92, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2158", "line": 94, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 94, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 101, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 101, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2267", "line": 101, "column": 22, "nodeType": "1878", "messageId": "1879", "endLine": 101, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 102, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 102, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2269", "line": 102, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 102, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2646", "line": 103, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 103, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2647", "line": 104, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 104, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2648", "line": 105, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 105, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2649", "line": 105, "column": 14, "nodeType": "1878", "messageId": "1879", "endLine": 105, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2159", "line": 106, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 106, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2650", "line": 106, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 106, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2352", "line": 107, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2651", "line": 107, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 107, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2583", "line": 118, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 118, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2652", "line": 118, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 118, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2653", "line": 125, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 125, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2654", "line": 125, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 125, "endColumn": 44}, {"ruleId": "1980", "severity": 1, "message": "2655", "line": 148, "column": 5, "nodeType": "1982", "endLine": 148, "endColumn": 60, "suggestions": "2656"}, {"ruleId": "1876", "severity": 1, "message": "2657", "line": 151, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 151, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2658", "line": 163, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 163, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2659", "line": 167, "column": 5, "nodeType": "1982", "endLine": 167, "endColumn": 60, "suggestions": "2660"}, {"ruleId": "1876", "severity": 1, "message": "2661", "line": 169, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 169, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2166", "line": 203, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 203, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2167", "line": 207, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 207, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 56, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 65}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 2, "column": 67, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 75}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 2, "column": 77, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 83}, {"ruleId": "1876", "severity": 1, "message": "2453", "line": 13, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2662", "line": 47, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 48}, {"ruleId": "1876", "severity": 1, "message": "2348", "line": 59, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 39}, {"ruleId": "1876", "severity": 1, "message": "2663", "line": 68, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 68, "endColumn": 41}, {"ruleId": "1876", "severity": 1, "message": "2664", "line": 74, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 74, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2372", "line": 1, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 4, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 4, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 4, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2665", "line": 23, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 23, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2005", "line": 24, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 24, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2666", "line": 26, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 26, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2001", "line": 27, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2667", "line": 28, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2668", "line": 29, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2292", "line": 85, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2324", "line": 4, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "1996", "line": 11, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "1997", "line": 12, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "1995", "line": 13, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2330", "line": 21, "column": 21, "nodeType": "1878", "messageId": "1879", "endLine": 21, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2331", "line": 22, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2332", "line": 22, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 44}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 82, "column": 22, "nodeType": "2032", "messageId": "2033", "endLine": 82, "endColumn": 24}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 82, "column": 53, "nodeType": "2032", "messageId": "2033", "endLine": 82, "endColumn": 55}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 85, "column": 36, "nodeType": "2032", "messageId": "2033", "endLine": 85, "endColumn": 38}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 85, "column": 63, "nodeType": "2032", "messageId": "2033", "endLine": 85, "endColumn": 65}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 88, "column": 36, "nodeType": "2032", "messageId": "2033", "endLine": 88, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2334", "line": 95, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 95, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2335", "line": 96, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 96, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2336", "line": 99, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 99, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2337", "line": 100, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2338", "line": 108, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 108, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2339", "line": 128, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 128, "endColumn": 16}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 268, "column": 45, "nodeType": "2032", "messageId": "2033", "endLine": 268, "endColumn": 47}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 268, "column": 104, "nodeType": "2032", "messageId": "2033", "endLine": 268, "endColumn": 106}, {"ruleId": "1876", "severity": 1, "message": "2669", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2380", "line": 4, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 8, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2670", "line": 15, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2671", "line": 15, "column": 30, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2672", "line": 15, "column": 46, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 60}, {"ruleId": "1876", "severity": 1, "message": "2673", "line": 15, "column": 62, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2674", "line": 15, "column": 80, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 96}, {"ruleId": "1876", "severity": 1, "message": "2675", "line": 17, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2676", "line": 17, "column": 35, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2142", "line": 17, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 55}, {"ruleId": "1876", "severity": 1, "message": "2677", "line": 22, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2678", "line": 58, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2679", "line": 66, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 66, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2392", "line": 72, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 72, "endColumn": 8}, {"ruleId": "1876", "severity": 1, "message": "2391", "line": 73, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 73, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2603", "line": 74, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 74, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 75, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 75, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2680", "line": 76, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 76, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2399", "line": 77, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 77, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2681", "line": 78, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 78, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2400", "line": 79, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 79, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2395", "line": 80, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 80, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2682", "line": 81, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 81, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2605", "line": 82, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2151", "line": 83, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 83, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2146", "line": 84, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 84, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2683", "line": 87, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 87, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2684", "line": 89, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 89, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2152", "line": 91, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 91, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2157", "line": 93, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 93, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2685", "line": 166, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 166, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2686", "line": 169, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 169, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2687", "line": 179, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 179, "endColumn": 21}, {"ruleId": "1980", "severity": 1, "message": "2688", "line": 332, "column": 5, "nodeType": "1982", "endLine": 332, "endColumn": 18, "suggestions": "2689"}, {"ruleId": "1876", "severity": 1, "message": "2402", "line": 599, "column": 33, "nodeType": "1878", "messageId": "1879", "endLine": 599, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2238", "line": 599, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 599, "endColumn": 60}, {"ruleId": "1876", "severity": 1, "message": "2191", "line": 599, "column": 62, "nodeType": "1878", "messageId": "1879", "endLine": 599, "endColumn": 67}, {"ruleId": "1876", "severity": 1, "message": "2151", "line": 599, "column": 69, "nodeType": "1878", "messageId": "1879", "endLine": 599, "endColumn": 85}, {"ruleId": "1980", "severity": 1, "message": "2690", "line": 927, "column": 3, "nodeType": "1982", "endLine": 927, "endColumn": 19, "suggestions": "2691"}, {"ruleId": "1876", "severity": 1, "message": "2692", "line": 999, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 999, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2693", "line": 1008, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 1008, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2382", "line": 11, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2324", "line": 14, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 14, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2694", "line": 67, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 67, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2695", "line": 180, "column": 86, "nodeType": "1878", "messageId": "1879", "endLine": 180, "endColumn": 101}, {"ruleId": "1876", "severity": 1, "message": "2400", "line": 184, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 184, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2696", "line": 563, "column": 5, "nodeType": "1982", "endLine": 563, "endColumn": 40, "suggestions": "2697"}, {"ruleId": "1980", "severity": 1, "message": "2698", "line": 586, "column": 6, "nodeType": "1982", "endLine": 586, "endColumn": 42, "suggestions": "2699"}, {"ruleId": "1980", "severity": 1, "message": "2700", "line": 600, "column": 6, "nodeType": "1982", "endLine": 600, "endColumn": 50, "suggestions": "2701"}, {"ruleId": "1980", "severity": 1, "message": "2702", "line": 877, "column": 5, "nodeType": "1982", "endLine": 877, "endColumn": 160, "suggestions": "2703"}, {"ruleId": "1980", "severity": 1, "message": "2704", "line": 945, "column": 5, "nodeType": "1982", "endLine": 945, "endColumn": 110, "suggestions": "2705"}, {"ruleId": "1980", "severity": 1, "message": "2706", "line": 975, "column": 5, "nodeType": "1982", "endLine": 975, "endColumn": 34, "suggestions": "2707"}, {"ruleId": "1980", "severity": 1, "message": "2708", "line": 993, "column": 5, "nodeType": "1982", "endLine": 993, "endColumn": 34, "suggestions": "2709"}, {"ruleId": "1980", "severity": 1, "message": "2708", "line": 1007, "column": 5, "nodeType": "1982", "endLine": 1007, "endColumn": 34, "suggestions": "2710"}, {"ruleId": "1980", "severity": 1, "message": "2708", "line": 1010, "column": 5, "nodeType": "1982", "endLine": 1010, "endColumn": 40, "suggestions": "2711"}, {"ruleId": "1876", "severity": 1, "message": "2712", "line": 1220, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1220, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2713", "line": 1223, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 1223, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 79}, {"ruleId": "1876", "severity": 1, "message": "1927", "line": 15, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 9}, {"ruleId": "1876", "severity": 1, "message": "2714", "line": 18, "column": 48, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 76}, {"ruleId": "1876", "severity": 1, "message": "2715", "line": 18, "column": 78, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 85}, {"ruleId": "1876", "severity": 1, "message": "2258", "line": 20, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2716", "line": 52, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2717", "line": 54, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 54, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2718", "line": 59, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2684", "line": 59, "column": 18, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2719", "line": 60, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "1962", "line": 61, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "1963", "line": 61, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2661", "line": 85, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2535", "line": 92, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 92, "endColumn": 25}, {"ruleId": "1980", "severity": 1, "message": "2720", "line": 183, "column": 5, "nodeType": "1982", "endLine": 183, "endColumn": 52, "suggestions": "2721"}, {"ruleId": "1980", "severity": 1, "message": "2036", "line": 183, "column": 6, "nodeType": "2085", "endLine": 183, "endColumn": 51}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 92, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 102}, {"ruleId": "1876", "severity": 1, "message": "2722", "line": 76, "column": 19, "nodeType": "1878", "messageId": "1879", "endLine": 76, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2425", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2252", "line": 2, "column": 36, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 48, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 57}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 2, "column": 59, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 67}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 2, "column": 69, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 75}, {"ruleId": "1876", "severity": 1, "message": "2253", "line": 2, "column": 77, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 84}, {"ruleId": "1876", "severity": 1, "message": "2723", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2724", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2725", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2726", "line": 9, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2422", "line": 1, "column": 29, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 37}, {"ruleId": "1876", "severity": 1, "message": "2372", "line": 1, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2177", "line": 1, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 59}, {"ruleId": "1876", "severity": 1, "message": "2379", "line": 1, "column": 61, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 67}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2117", "line": 2, "column": 56, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 62}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2248", "line": 2, "column": 80, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2250", "line": 2, "column": 105, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 111}, {"ruleId": "1876", "severity": 1, "message": "2251", "line": 2, "column": 113, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 121}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2426", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2213", "line": 4, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 4, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 4, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 4, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 7, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 8, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 9, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 10, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 11, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2549", "line": 12, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2727", "line": 19, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "1909", "line": 2, "column": 64, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 78}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2139", "line": 2, "column": 177, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 193}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 4, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 4, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 4, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 5, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 7, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 8, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 9, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 10, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 11, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2519", "line": 27, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2520", "line": 28, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2521", "line": 29, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 29, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2522", "line": 30, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 30, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2525", "line": 38, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 38, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2526", "line": 39, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2527", "line": 41, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 41, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2554", "line": 42, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 42, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2555", "line": 43, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2728", "line": 44, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2556", "line": 45, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2558", "line": 47, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2559", "line": 48, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 33}, {"ruleId": "1876", "severity": 1, "message": "2560", "line": 49, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2562", "line": 51, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 30}, {"ruleId": "1876", "severity": 1, "message": "2563", "line": 52, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2564", "line": 53, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 53, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2565", "line": 54, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 54, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 58, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2729", "line": 152, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 152, "endColumn": 40}, {"ruleId": "1876", "severity": 1, "message": "2730", "line": 153, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 153, "endColumn": 41}, {"ruleId": "1876", "severity": 1, "message": "2731", "line": 154, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 154, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "1962", "line": 156, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 156, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2732", "line": 185, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 185, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2533", "line": 220, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 220, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2534", "line": 223, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 223, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2535", "line": 228, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 228, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2069", "line": 245, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 245, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2567", "line": 249, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 249, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2573", "line": 254, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 254, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2733", "line": 263, "column": 6, "nodeType": "1982", "endLine": 263, "endColumn": 8, "suggestions": "2734"}, {"ruleId": "1876", "severity": 1, "message": "2735", "line": 298, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 298, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2505", "line": 1, "column": 17, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2379", "line": 1, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 55}, {"ruleId": "1876", "severity": 1, "message": "2736", "line": 1, "column": 69, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 80}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2249", "line": 2, "column": 93, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 103}, {"ruleId": "1876", "severity": 1, "message": "2506", "line": 2, "column": 123, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 140}, {"ruleId": "1876", "severity": 1, "message": "2136", "line": 2, "column": 142, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 158}, {"ruleId": "1876", "severity": 1, "message": "2507", "line": 2, "column": 160, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 166}, {"ruleId": "1876", "severity": 1, "message": "2380", "line": 2, "column": 195, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 212}, {"ruleId": "1876", "severity": 1, "message": "2510", "line": 5, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 49}, {"ruleId": "1876", "severity": 1, "message": "2511", "line": 5, "column": 51, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 71}, {"ruleId": "1876", "severity": 1, "message": "2512", "line": 5, "column": 73, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 91}, {"ruleId": "1876", "severity": 1, "message": "2513", "line": 6, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 31}, {"ruleId": "1876", "severity": 1, "message": "2518", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2519", "line": 9, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2520", "line": 10, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2521", "line": 11, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 11, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2522", "line": 12, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2523", "line": 13, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2514", "line": 15, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2515", "line": 16, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2516", "line": 17, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 17, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2517", "line": 18, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 18, "endColumn": 10}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 19, "column": 5, "nodeType": "1878", "messageId": "1879", "endLine": 19, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2724", "line": 34, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2737", "line": 44, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2525", "line": 45, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2526", "line": 46, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2527", "line": 48, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2554", "line": 49, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2555", "line": 50, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2728", "line": 51, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2556", "line": 52, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 52, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2558", "line": 54, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 54, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2559", "line": 55, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 55, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2560", "line": 56, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2562", "line": 58, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2563", "line": 59, "column": 6, "nodeType": "1878", "messageId": "1879", "endLine": 59, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2564", "line": 60, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2565", "line": 61, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 61, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2738", "line": 63, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 63, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2440", "line": 66, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 66, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2729", "line": 89, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 89, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2730", "line": 90, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 90, "endColumn": 43}, {"ruleId": "1876", "severity": 1, "message": "2731", "line": 91, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 91, "endColumn": 46}, {"ruleId": "1980", "severity": 1, "message": "2739", "line": 112, "column": 5, "nodeType": "1982", "endLine": 112, "endColumn": 52, "suggestions": "2740"}, {"ruleId": "1876", "severity": 1, "message": "2533", "line": 117, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 117, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2534", "line": 120, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 120, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2535", "line": 125, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 125, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2741", "line": 135, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 135, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2069", "line": 175, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 175, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2742", "line": 183, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 183, "endColumn": 20}, {"ruleId": "1980", "severity": 1, "message": "2733", "line": 188, "column": 4, "nodeType": "1982", "endLine": 188, "endColumn": 6, "suggestions": "2743"}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 193, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 193, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 194, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 194, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 194, "column": 39, "nodeType": "2032", "messageId": "2033", "endLine": 194, "endColumn": 41}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 213, "column": 19, "nodeType": "2032", "messageId": "2033", "endLine": 213, "endColumn": 21}, {"ruleId": "2030", "severity": 1, "message": "2052", "line": 226, "column": 20, "nodeType": "2032", "messageId": "2033", "endLine": 226, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2744", "line": 279, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 279, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "1962", "line": 307, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 307, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2732", "line": 334, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 334, "endColumn": 23}, {"ruleId": "1980", "severity": 1, "message": "2745", "line": 371, "column": 4, "nodeType": "1982", "endLine": 371, "endColumn": 6, "suggestions": "2746"}, {"ruleId": "1876", "severity": 1, "message": "2176", "line": 2, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "2246", "line": 2, "column": 38, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2268", "line": 9, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2267", "line": 9, "column": 22, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 36}, {"ruleId": "1876", "severity": 1, "message": "1986", "line": 10, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2269", "line": 10, "column": 26, "nodeType": "1878", "messageId": "1879", "endLine": 10, "endColumn": 44}, {"ruleId": "1876", "severity": 1, "message": "2647", "line": 12, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2719", "line": 12, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 12, "endColumn": 46}, {"ruleId": "1876", "severity": 1, "message": "2648", "line": 13, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2649", "line": 13, "column": 14, "nodeType": "1878", "messageId": "1879", "endLine": 13, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2352", "line": 15, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 15, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2583", "line": 16, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2747", "line": 28, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 28, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2748", "line": 3, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 3, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2518", "line": 6, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 32, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 42}, {"ruleId": "1876", "severity": 1, "message": "2449", "line": 2, "column": 44, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 53}, {"ruleId": "1876", "severity": 1, "message": "2453", "line": 4, "column": 46, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 65}, {"ruleId": "1876", "severity": 1, "message": "1907", "line": 4, "column": 67, "nodeType": "1878", "messageId": "1879", "endLine": 4, "endColumn": 75}, {"ruleId": "1876", "severity": 1, "message": "2749", "line": 8, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2678", "line": 16, "column": 13, "nodeType": "1878", "messageId": "1879", "endLine": 16, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2750", "line": 31, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2282", "line": 33, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 33, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2751", "line": 43, "column": 6, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2752", "line": 85, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 85, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2474", "line": 95, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 95, "endColumn": 26}, {"ruleId": "1876", "severity": 1, "message": "2725", "line": 5, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2726", "line": 6, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2753", "line": 7, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2754", "line": 27, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 27, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2755", "line": 34, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 34, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2447", "line": 56, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 56, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2448", "line": 58, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 58, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2756", "line": 80, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 80, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2757", "line": 82, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2758", "line": 82, "column": 23, "nodeType": "1878", "messageId": "1879", "endLine": 82, "endColumn": 38}, {"ruleId": "1876", "severity": 1, "message": "2759", "line": 133, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 133, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2760", "line": 290, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 290, "endColumn": 28}, {"ruleId": "1980", "severity": 1, "message": "2761", "line": 344, "column": 5, "nodeType": "1982", "endLine": 344, "endColumn": 22, "suggestions": "2762"}, {"ruleId": "1876", "severity": 1, "message": "2669", "line": 1, "column": 58, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 67}, {"ruleId": "1876", "severity": 1, "message": "2425", "line": 2, "column": 15, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2178", "line": 2, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 34}, {"ruleId": "1876", "severity": 1, "message": "2677", "line": 5, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 11}, {"ruleId": "1876", "severity": 1, "message": "2133", "line": 6, "column": 41, "nodeType": "1878", "messageId": "1879", "endLine": 6, "endColumn": 53}, {"ruleId": "1876", "severity": 1, "message": "2675", "line": 7, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2142", "line": 7, "column": 16, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 22}, {"ruleId": "1876", "severity": 1, "message": "2763", "line": 7, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 29}, {"ruleId": "1876", "severity": 1, "message": "2764", "line": 7, "column": 31, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2304", "line": 7, "column": 37, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 47}, {"ruleId": "1876", "severity": 1, "message": "2676", "line": 7, "column": 49, "nodeType": "1878", "messageId": "1879", "endLine": 7, "endColumn": 61}, {"ruleId": "1876", "severity": 1, "message": "2426", "line": 8, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 8, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2724", "line": 9, "column": 8, "nodeType": "1878", "messageId": "1879", "endLine": 9, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2765", "line": 40, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2766", "line": 41, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 41, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "1993", "line": 42, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 42, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2767", "line": 43, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2311", "line": 44, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2768", "line": 45, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 45, "endColumn": 18}, {"ruleId": "1876", "severity": 1, "message": "2310", "line": 46, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 16}, {"ruleId": "1876", "severity": 1, "message": "2309", "line": 47, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 47, "endColumn": 15}, {"ruleId": "1876", "severity": 1, "message": "2769", "line": 48, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 12}, {"ruleId": "1876", "severity": 1, "message": "2307", "line": 50, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 50, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2770", "line": 51, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 51, "endColumn": 24}, {"ruleId": "1876", "severity": 1, "message": "2389", "line": 55, "column": 4, "nodeType": "1878", "messageId": "1879", "endLine": 55, "endColumn": 11}, {"ruleId": "1980", "severity": 1, "message": "2771", "line": 156, "column": 7, "nodeType": "1982", "endLine": 156, "endColumn": 11, "suggestions": "2772"}, {"ruleId": "1876", "severity": 1, "message": "2138", "line": 2, "column": 2, "nodeType": "1878", "messageId": "1879", "endLine": 2, "endColumn": 14}, {"ruleId": "1876", "severity": 1, "message": "2773", "line": 20, "column": 24, "nodeType": "1878", "messageId": "1879", "endLine": 20, "endColumn": 35}, {"ruleId": "1876", "severity": 1, "message": "2279", "line": 42, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 42, "endColumn": 21}, {"ruleId": "1876", "severity": 1, "message": "2384", "line": 43, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 43, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2154", "line": 44, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 44, "endColumn": 28}, {"ruleId": "1876", "severity": 1, "message": "2774", "line": 46, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 46, "endColumn": 13}, {"ruleId": "1876", "severity": 1, "message": "2239", "line": 48, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 48, "endColumn": 17}, {"ruleId": "1876", "severity": 1, "message": "2282", "line": 49, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 49, "endColumn": 20}, {"ruleId": "1876", "severity": 1, "message": "2263", "line": 60, "column": 10, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2264", "line": 60, "column": 45, "nodeType": "1878", "messageId": "1879", "endLine": 60, "endColumn": 62}, {"ruleId": "1876", "severity": 1, "message": "2775", "line": 100, "column": 11, "nodeType": "1878", "messageId": "1879", "endLine": 100, "endColumn": 27}, {"ruleId": "1876", "severity": 1, "message": "2776", "line": 103, "column": 12, "nodeType": "1878", "messageId": "1879", "endLine": 103, "endColumn": 26}, {"ruleId": "1980", "severity": 1, "message": "2479", "line": 128, "column": 5, "nodeType": "1982", "endLine": 128, "endColumn": 155, "suggestions": "2777"}, {"ruleId": "1876", "severity": 1, "message": "2433", "line": 1, "column": 27, "nodeType": "1878", "messageId": "1879", "endLine": 1, "endColumn": 41}, {"ruleId": "1876", "severity": 1, "message": "2778", "line": 22, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 22, "endColumn": 23}, {"ruleId": "1876", "severity": 1, "message": "2779", "line": 31, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 31, "endColumn": 19}, {"ruleId": "1876", "severity": 1, "message": "2780", "line": 40, "column": 7, "nodeType": "1878", "messageId": "1879", "endLine": 40, "endColumn": 25}, {"ruleId": "1876", "severity": 1, "message": "2781", "line": 5, "column": 3, "nodeType": "1878", "messageId": "1879", "endLine": 5, "endColumn": 24}, {"ruleId": "1980", "severity": 1, "message": "2782", "line": 121, "column": 6, "nodeType": "1982", "endLine": 121, "endColumn": 26, "suggestions": "2783"}, {"ruleId": "1876", "severity": 1, "message": "2784", "line": 39, "column": 9, "nodeType": "1878", "messageId": "1879", "endLine": 39, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'isAppReady' is assigned a value but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2785"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2786"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2787"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1052) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2788"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2789"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2790"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'resetALTKeywordForNewTooltip', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2791"], "'handleElementSelectionToggle' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2792"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2793"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2794"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2795"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2796"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2797"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2798"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2799"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2800"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2801"], "'getAccountIdForUpdate' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2802", "2803"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2804"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2805"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2806"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2807"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2808"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2809"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2810"], "'handleEnableAI' is assigned a value but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2811"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2812"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2813"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2814"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2815"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2816"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2817"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2818"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2819"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2820"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2821"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2822"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2823"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2824"], ["2825"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2826"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["2827"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2828"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2829"], ["2830"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "React Hook useMemo has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2831"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2832"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2833"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2834"], ["2835"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2836"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2837"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2838"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2839"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2840"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2841"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2842"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2843"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2844"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2845"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2846"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2847"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2848"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2849"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2850"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2851"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2852"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2853"], ["2854"], ["2855"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2856"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2857"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2858"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2859"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2860"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2861"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "React Hook useMemo has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["2862"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2863"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["2864"], "'orgId' is assigned a value but never used.", {"desc": "2865", "fix": "2866"}, {"desc": "2867", "fix": "2868"}, {"desc": "2869", "fix": "2870"}, {"desc": "2871", "fix": "2872"}, {"desc": "2873", "fix": "2874"}, {"desc": "2875", "fix": "2876"}, {"desc": "2877", "fix": "2878"}, {"desc": "2879", "fix": "2880"}, {"desc": "2881", "fix": "2882"}, {"desc": "2883", "fix": "2884"}, {"desc": "2885", "fix": "2886"}, {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"messageId": "2899", "fix": "2900", "desc": "2901"}, {"messageId": "2902", "fix": "2903", "desc": "2904"}, {"desc": "2905", "fix": "2906"}, {"desc": "2907", "fix": "2908"}, {"desc": "2909", "fix": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"messageId": "2935", "data": "2936", "fix": "2937", "desc": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2941", "fix": "2942"}, {"desc": "2943", "fix": "2944"}, {"desc": "2945", "fix": "2946"}, {"desc": "2947", "fix": "2948"}, {"desc": "2949", "fix": "2950"}, {"desc": "2951", "fix": "2952"}, {"desc": "2953", "fix": "2954"}, {"desc": "2955", "fix": "2956"}, {"desc": "2957", "fix": "2958"}, {"desc": "2959", "fix": "2960"}, {"desc": "2961", "fix": "2962"}, {"desc": "2963", "fix": "2964"}, {"desc": "2965", "fix": "2966"}, {"desc": "2967", "fix": "2968"}, {"desc": "2919", "fix": "2969"}, {"desc": "2970", "fix": "2971"}, {"desc": "2972", "fix": "2973"}, {"desc": "2974", "fix": "2975"}, {"desc": "2976", "fix": "2977"}, {"desc": "2978", "fix": "2979"}, {"desc": "2970", "fix": "2980"}, {"desc": "2981", "fix": "2982"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2991", "fix": "2992"}, {"desc": "2993", "fix": "2994"}, {"desc": "2995", "fix": "2996"}, {"desc": "2997", "fix": "2998"}, {"desc": "2999", "fix": "3000"}, {"desc": "3001", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3003", "fix": "3005"}, {"desc": "3006", "fix": "3007"}, {"desc": "3008", "fix": "3009"}, {"desc": "3010", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3010", "fix": "3014"}, {"desc": "3015", "fix": "3016"}, {"desc": "3017", "fix": "3018"}, {"desc": "2865", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"desc": "3022", "fix": "3023"}, "Update the dependencies array to be: []", {"range": "3024", "text": "3025"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3026", "text": "3027"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3028", "text": "3029"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3030", "text": "3031"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3032", "text": "3033"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3034", "text": "3035"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", {"range": "3036", "text": "3037"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3038", "text": "3039"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3040", "text": "3041"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3042", "text": "3043"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3044", "text": "3045"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3046", "text": "3047"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3048", "text": "3049"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3050", "text": "3051"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3052", "text": "3053"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3054", "text": "3055"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3056", "text": "3057"}, "removeEscape", {"range": "3058", "text": "3059"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3060", "text": "3061"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3062", "text": "3063"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3064", "text": "3065"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3066", "text": "3067"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3068", "text": "3069"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3070", "text": "3071"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3072", "text": "3073"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3074", "text": "3075"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3076", "text": "3077"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3078", "text": "3079"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3080", "text": "3081"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3082", "text": "3083"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3084", "text": "3085"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3086", "text": "3087"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3088", "text": "3089"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3090", "text": "3091"}, "suggestString", {"type": "3092"}, {"range": "3093", "text": "3094"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3095", "text": "3096"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3097", "text": "3098"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3099", "text": "3100"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3101", "text": "3102"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3103", "text": "3104"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3105", "text": "3106"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3107", "text": "3108"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3109", "text": "3110"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3111", "text": "3112"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3113", "text": "3114"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3115", "text": "3116"}, "Update the dependencies array to be: [handlePaste, isRtlDirection, selectedTemplate, selectedTemplateTour]", {"range": "3117", "text": "3118"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3119", "text": "3120"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3121", "text": "3122"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3123", "text": "3124"}, {"range": "3125", "text": "3077"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3126", "text": "3127"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3128", "text": "3129"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3130", "text": "3131"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3132", "text": "3133"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3134", "text": "3135"}, {"range": "3136", "text": "3127"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3137", "text": "3138"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3139", "text": "3140"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3141", "text": "3142"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3143", "text": "3144"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3145", "text": "3146"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3147", "text": "3148"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3149", "text": "3150"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3151", "text": "3152"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3153", "text": "3154"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3155", "text": "3156"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3157", "text": "3158"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3159", "text": "3160"}, {"range": "3161", "text": "3160"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3162", "text": "3163"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3164", "text": "3165"}, "Update the dependencies array to be: [fetchData]", {"range": "3166", "text": "3167"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3168", "text": "3169"}, {"range": "3170", "text": "3167"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3171", "text": "3172"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3173", "text": "3174"}, {"range": "3175", "text": "3025"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3176", "text": "3177"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3178", "text": "3179"}, [18210, 18232], "[]", [26521, 26562], "[fetchGuideDetails, hotspot, hotspotClicked]", [27044, 27057], "[designPopup, setDesignPopup]", [29781, 29928], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30406, 30766], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [35410, 35444], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [70745, 70758], "[currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", [86801, 86834], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [101433, 101447], "[createWithAI, setIsUnSavedChanges, stepCreation]", [124549, 124577], "[isLoggedIn, organizationId, userType]", [130523, 130675], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [160313, 160370], "[currentGuide?.GuideStep, currentStep]", [165627, 165644], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [171576, 171609], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [172319, 172420], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [176520, 176532], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [176952, 177025], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [198326, 198327], "", [198326, 198326], "\\", [4501, 4503], "[loggedOut]", [16077, 16132], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17506, 17561], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18006, 18008], "[selectedActions.value, targetURL]", [2347, 2349], "[isExtensionClosed, setIsExtensionClosed]", [3014, 3037], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3381, 3424], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [22776, 22871], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26739, 26756], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8894, 8939], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9245, 9258], "[fetchAnnouncements, searchQuery]", [15806, 15862], "[handlePaste, isRtlDirection, selectedTemplate, selectedTemplateTour]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [9769, 9782], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26416, 26432], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [4589, 4593], [4246, 4396], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]"]